'use client';

import { useAuth } from '@/lib/auth/AuthProvider';
import { useHistoryStore } from '@/store/historyStore';
import { createContext, ReactNode, useContext, useEffect } from 'react';

interface HistoryContextValue {
  userId: string;
  agentId: string | null;
  isReady: boolean;
}

const HistoryContext = createContext<HistoryContextValue | null>(null);

interface HistoryProviderProps {
  children: ReactNode;
}

export function HistoryProvider({ children }: HistoryProviderProps) {
  const { user } = useAuth();
  const reset = useHistoryStore(state => state.reset);
  const selectedAgentId = useHistoryStore(state => state.selectedAgentId);

  // 确保用户已登录
  const userId = user?.id || '';
  const isReady = Boolean(userId);

  // 用户变化时重置状态
  useEffect(() => {
    if (!isReady) {
      reset();
    }
  }, [isReady, reset]);

  // 如果用户未登录，显示登录提示
  if (!isReady) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <div className="text-text-muted text-lg">请先登录</div>
        </div>
      </div>
    );
  }

  const contextValue: HistoryContextValue = {
    userId,
    agentId: selectedAgentId,
    isReady,
  };

  return (
    <HistoryContext.Provider value={contextValue}>
      {children}
    </HistoryContext.Provider>
  );
}

export function useHistoryContext(): HistoryContextValue {
  const context = useContext(HistoryContext);
  if (!context) {
    throw new Error('useHistoryContext must be used within a HistoryProvider');
  }
  return context;
}
