'use client';

/**
 * 统一API调用管理器
 * 提供请求去重、缓存、错误处理等功能
 * 确保全局唯一的API调用入口
 */

import { ConversationItem, ConversationMessage } from '@/types/conversation';
import { conversationsResponse } from '@/utils/api-compatibility';

// 请求状态枚举
enum RequestStatus {
  PENDING = 'pending',
  FULFILLED = 'fulfilled',
  REJECTED = 'rejected',
}

// 请求记录接口
interface RequestRecord {
  key: string;
  status: RequestStatus;
  promise: Promise<any>;
  timestamp: number;
  ttl: number;
  result?: any;
  error?: Error;
}

// 缓存记录接口
interface CacheRecord {
  key: string;
  data: any;
  timestamp: number;
  ttl: number;
}

// API管理器类
class ApiManager {
  private requests = new Map<string, RequestRecord>();
  private cache = new Map<string, CacheRecord>();
  private readonly defaultTtl = 5 * 60 * 1000; // 5分钟默认TTL

  /**
   * 通用请求方法，带去重和缓存
   */
  async request<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: {
      cache?: boolean;
      cacheTtl?: number;
      requestTtl?: number;
      forceRefresh?: boolean;
    } = {}
  ): Promise<T> {
    const {
      cache = true,
      cacheTtl = this.defaultTtl,
      requestTtl = 30 * 1000, // 30秒请求超时
      forceRefresh = false,
    } = options;

    console.log(`🔄 API请求: ${key}`, { cache, forceRefresh });

    // 1. 检查缓存
    if (cache && !forceRefresh) {
      const cached = this.getFromCache<T>(key);
      if (cached) {
        console.log(`📋 缓存命中: ${key}`);
        return cached;
      }
    }

    // 2. 检查是否有进行中的请求
    const existingRequest = this.requests.get(key);
    if (existingRequest && existingRequest.status === RequestStatus.PENDING) {
      console.log(`⏳ 请求去重: ${key}`);
      return existingRequest.promise;
    }

    // 3. 创建新请求
    const requestPromise = this.createRequest(key, requestFn, requestTtl);

    // 4. 记录请求
    const requestRecord: RequestRecord = {
      key,
      status: RequestStatus.PENDING,
      promise: requestPromise,
      timestamp: Date.now(),
      ttl: requestTtl,
    };

    this.requests.set(key, requestRecord);

    try {
      const result = await requestPromise;

      // 5. 更新请求状态
      requestRecord.status = RequestStatus.FULFILLED;
      requestRecord.result = result;

      // 6. 缓存结果
      if (cache) {
        this.setCache(key, result, cacheTtl);
      }

      console.log(`✅ 请求成功: ${key}`);
      return result;
    } catch (error) {
      // 7. 处理错误
      requestRecord.status = RequestStatus.REJECTED;
      requestRecord.error = error as Error;

      console.error(`❌ 请求失败: ${key}`, error);
      throw error;
    } finally {
      // 8. 清理过期请求记录
      setTimeout(() => {
        this.requests.delete(key);
      }, requestTtl);
    }
  }

  /**
   * 创建带超时的请求
   */
  private createRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`请求超时: ${key}`));
      }, timeout);

      requestFn()
        .then(resolve)
        .catch(reject)
        .finally(() => {
          clearTimeout(timeoutId);
        });
    });
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      key,
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * 清除缓存
   */
  invalidateCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      console.log('🗑️ 清除所有缓存');
      return;
    }

    const keysToDelete: string[] = [];
    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(`🗑️ 清除缓存匹配: ${pattern}`, keysToDelete);
  }

  /**
   * 取消请求
   */
  cancelRequest(key: string): void {
    const request = this.requests.get(key);
    if (request && request.status === RequestStatus.PENDING) {
      this.requests.delete(key);
      console.log(`🚫 取消请求: ${key}`);
    }
  }

  /**
   * 清理过期的请求和缓存
   */
  cleanup(): void {
    const now = Date.now();

    // 清理过期请求
    for (const [key, request] of this.requests) {
      if (now - request.timestamp > request.ttl) {
        this.requests.delete(key);
      }
    }

    // 清理过期缓存
    for (const [key, cache] of this.cache) {
      if (now - cache.timestamp > cache.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      activeRequests: this.requests.size,
      cacheSize: this.cache.size,
      requests: Array.from(this.requests.entries()).map(([key, record]) => ({
        key,
        status: record.status,
        age: Date.now() - record.timestamp,
      })),
      cacheKeys: Array.from(this.cache.keys()),
    };
  }
}

// 全局单例实例
export const apiManager = new ApiManager();

// 定期清理过期数据
setInterval(() => {
  apiManager.cleanup();
}, 60 * 1000); // 每分钟清理一次

// 错误处理集成
let globalErrorHandler: ((error: any) => void) | null = null;

/**
 * 设置全局错误处理器
 */
export function setGlobalErrorHandler(handler: (error: any) => void): void {
  globalErrorHandler = handler;
}

/**
 * 触发全局错误处理
 */
function triggerGlobalError(error: any, context: string): void {
  console.error(`API错误 [${context}]:`, error);
  if (globalErrorHandler) {
    globalErrorHandler(error);
  }
}

// 会话相关的API封装
export class ConversationApi {
  private static instance: ConversationApi;

  static getInstance(): ConversationApi {
    if (!ConversationApi.instance) {
      ConversationApi.instance = new ConversationApi();
    }
    return ConversationApi.instance;
  }

  /**
   * 获取会话列表
   */
  async getConversations(
    userId: string,
    page: number = 1,
    pageSize: number = 20,
    forceRefresh: boolean = false
  ): Promise<{ data: ConversationItem[]; pagination?: any }> {
    const key = `conversations-${userId}-${page}-${pageSize}`;

    try {
      return await apiManager.request(
        key,
        async () => {
          const { getConversations, buildGetConversationsParams } =
            await import('@/services/conversation');
          const params = buildGetConversationsParams(userId, page, pageSize);
          const response = await getConversations(params);
          return conversationsResponse(page, pageSize, response);
        },
        {
          cache: true,
          cacheTtl: 5 * 60 * 1000, // 5分钟缓存
          forceRefresh,
        }
      );
    } catch (error) {
      // 特定错误处理
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('认证')) {
          // 认证错误时清除所有缓存
          apiManager.invalidateCache();
        } else if (error.message.includes('403')) {
          // 权限错误时清除相关缓存
          apiManager.invalidateCache(`conversations-${userId}`);
        }
      }

      triggerGlobalError(error, `getConversations-${userId}`);
      throw error;
    }
  }

  /**
   * 创建会话
   */
  async createConversation(agentId: string): Promise<ConversationItem> {
    const key = `create-conversation-${agentId}-${Date.now()}`;

    try {
      const result = await apiManager.request(
        key,
        async () => {
          const { createConversation } = await import(
            '@/services/conversation'
          );
          return await createConversation(agentId);
        },
        {
          cache: false, // 创建操作不缓存
        }
      );

      // 创建成功后，清除相关缓存
      apiManager.invalidateCache(`conversations-${agentId}`);

      return result;
    } catch (error) {
      triggerGlobalError(error, `createConversation-${agentId}`);
      throw error;
    }
  }

  /**
   * 删除会话
   */
  async deleteConversation(
    conversationId: string,
    agentId: string
  ): Promise<void> {
    const key = `delete-conversation-${conversationId}`;

    try {
      await apiManager.request(
        key,
        async () => {
          const { deleteConversation } = await import(
            '@/services/conversation'
          );
          return await deleteConversation(conversationId);
        },
        {
          cache: false, // 删除操作不缓存
        }
      );

      // 删除成功后，清除相关缓存
      apiManager.invalidateCache(`conversations-${agentId}`);
      apiManager.invalidateCache(`messages-${conversationId}`);
    } catch (error) {
      triggerGlobalError(error, `deleteConversation-${conversationId}`);
      throw error;
    }
  }

  /**
   * 重命名会话
   */
  async renameConversation(
    conversationId: string,
    newTitle: string,
    agentId: string
  ): Promise<void> {
    const key = `rename-conversation-${conversationId}`;

    try {
      await apiManager.request(
        key,
        async () => {
          const { updateConversationTitle } = await import(
            '@/services/conversation'
          );
          return await updateConversationTitle({
            conversationId,
            newTitleAlias: newTitle,
          });
        },
        {
          cache: false, // 更新操作不缓存
        }
      );

      // 重命名成功后，清除相关缓存
      apiManager.invalidateCache(`conversations-${agentId}`);
    } catch (error) {
      triggerGlobalError(error, `renameConversation-${conversationId}`);
      throw error;
    }
  }

  /**
   * 获取会话消息
   */
  async getConversationMessages(
    conversationId: string,
    forceRefresh: boolean = false
  ): Promise<ConversationMessage[]> {
    const key = `messages-${conversationId}`;

    try {
      return await apiManager.request(
        key,
        async () => {
          // 调用实际的消息API
          const { getConversationMessages } = await import(
            '@/services/conversation'
          );
          return await getConversationMessages(conversationId);
        },
        {
          cache: true,
          cacheTtl: 2 * 60 * 1000, // 2分钟缓存
          forceRefresh,
        }
      );
    } catch (error) {
      // 特定错误处理
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('认证')) {
          // 认证错误时清除所有缓存
          apiManager.invalidateCache();
        } else if (error.message.includes('404')) {
          // 会话不存在时清除该会话相关缓存
          apiManager.invalidateCache(`messages-${conversationId}`);
        }
      }

      triggerGlobalError(error, `getConversationMessages-${conversationId}`);
      throw error;
    }
  }

  /**
   * 发送消息
   */
  async sendMessage({
    agentId,
    conversationId,
    text,
  }: {
    agentId: string;
    conversationId: string;
    text: string;
  }): Promise<ConversationMessage> {
    const key = `send-message-${agentId}-${conversationId}-${Date.now()}`;

    try {
      const result = await apiManager.request(
        key,
        async () => {
          const { sendMessage } = await import('@/services/conversation');
          return await sendMessage({
            agentId,
            conversationId,
            text,
          });
        },
        {
          cache: false, // 发送操作不缓存
        }
      );

      return result;
    } catch (error) {
      triggerGlobalError(error, `sendMessage-${agentId}-${conversationId}`);
      throw error;
    }
  }
}

// 导出单例实例
export const conversationApi = ConversationApi.getInstance();

// 导出便捷方法
export const {
  getConversations,
  createConversation,
  deleteConversation,
  renameConversation,
  getConversationMessages,
} = conversationApi;

// 调试用的统计信息
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).apiManager = apiManager;
  (window as any).conversationApi = conversationApi;
}
