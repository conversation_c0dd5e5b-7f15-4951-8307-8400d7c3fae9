'use client';

import { usePageConfigContext } from '@/contexts/PageConfigContext';
import {
  BreadcrumbConfig,
  BreadcrumbSetter,
  PageConfig,
} from '@/types/pageConfig';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect } from 'react';

/**
 * 静态路由面包屑配置
 */
const staticBreadcrumbConfig: Record<string, string> = {
  '/dashboard': '控制面板',
  '/chat': '聊天',
  '/files': '文件',
  '/knowledge': '知识库',
  '/ai-staff': 'AI员工',
  '/history': '历史会话',
  '/ai-staff/create': '创建员工',
  '/knowledge/dataset': '数据集',
  '/knowledge/dataset/chunk': '文档切片',
};

/**
 * 获取静态路由的面包屑名称
 */
function getStaticBreadcrumbName(path: string): string {
  return staticBreadcrumbConfig[path] || '未知页面';
}

/**
 * 页面配置管理 Hook
 */
export function usePageConfig() {
  const { config, setConfig, resetConfig } = usePageConfigContext();
  const pathname = usePathname();

  /**
   * 设置面包屑配置
   */
  const setBreadcrumb: BreadcrumbSetter = useCallback(
    (breadcrumbConfig: BreadcrumbConfig) => {
      setConfig({ breadcrumb: breadcrumbConfig });
    },
    [setConfig]
  );

  /**
   * 设置页面标题
   */
  const setTitle = useCallback(
    (title: string) => {
      setConfig({ title });
    },
    [setConfig]
  );

  /**
   * 设置页面操作按钮
   */
  const setActions = useCallback(
    (actions: PageConfig['actions']) => {
      setConfig({ actions });
    },
    [setConfig]
  );

  /**
   * 设置页面元数据
   */
  const setMeta = useCallback(
    (meta: Record<string, any>) => {
      setConfig({ meta });
    },
    [setConfig]
  );

  return {
    // 当前配置
    config,

    // 配置方法
    setConfig,
    setBreadcrumb,
    setTitle,
    setActions,
    setMeta,
    resetConfig,
  };
}

/**
 * 页面配置初始化 Hook
 * 在路由变化时自动设置默认面包屑
 * 应该在布局组件中调用，确保只有一个实例
 */
export function usePageConfigInit() {
  const { resetConfig, setConfig } = usePageConfigContext();
  const pathname = usePathname();

  useEffect(() => {
    // 重置配置
    resetConfig();

    // 设置默认面包屑
    const defaultTitle = getStaticBreadcrumbName(pathname);
    setConfig({
      breadcrumb: {
        title: defaultTitle,
        loading: false,
      },
    });
  }, [pathname]);
}

/**
 * 面包屑专用 Hook
 * 提供更简洁的面包屑操作接口
 */
export function useBreadcrumb() {
  const { config, setConfig } = usePageConfigContext();

  /**
   * 设置面包屑标题
   */
  const setTitle = useCallback(
    (title: string, loading = false) => {
      setConfig({
        breadcrumb: {
          title,
          loading,
        },
      });
    },
    [setConfig]
  );

  /**
   * 设置面包屑加载状态
   */
  const setLoading = useCallback(
    (loading: boolean) => {
      setConfig({
        breadcrumb: {
          title: config.breadcrumb?.title || '',
          ...config.breadcrumb,
          loading,
        },
      });
    },
    [setConfig, config.breadcrumb]
  );

  /**
   * 设置自定义面包屑项
   */
  const setItems = useCallback(
    (items: BreadcrumbConfig['items']) => {
      setConfig({
        breadcrumb: {
          title: config.breadcrumb?.title || '',
          ...config.breadcrumb,
          items,
        },
      });
    },
    [setConfig, config.breadcrumb]
  );

  return {
    // 当前面包屑配置
    breadcrumb: config.breadcrumb,

    // 操作方法
    setTitle,
    setLoading,
    setItems,

    // 便捷方法
    setBreadcrumb: useCallback(
      (breadcrumbConfig: BreadcrumbConfig) => {
        setConfig({ breadcrumb: breadcrumbConfig });
      },
      [setConfig]
    ),
  };
}
