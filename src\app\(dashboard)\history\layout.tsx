'use client';

import EnvironmentChecker from '@/components/common/EnvironmentChecker';
import { HistoryProvider } from '@/components/history/HistoryProvider';
import ConversationList from '@/components/history/ConversationList';
import React from 'react';

interface HistoryLayoutProps {
  children: React.ReactNode;
}

export default function HistoryLayout({ children }: HistoryLayoutProps) {
  return (
    <EnvironmentChecker>
      <HistoryProvider>
        <div className="flex h-full overflow-hidden">
          {/* 左侧会话列表 */}
          <ConversationList />

          {/* 右侧内容区域 */}
          <div className="flex-1 flex flex-col h-full overflow-hidden">
            {children}
          </div>
        </div>
      </HistoryProvider>
    </EnvironmentChecker>
  );
}
