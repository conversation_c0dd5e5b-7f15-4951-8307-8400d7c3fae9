import { IFile } from '@/interfaces/database/file-manager';
import FileCard from '../file-card';

interface FileCardListProps {
  files: IFile[];
  onNavigate: (folderId: string) => void;
  onRename: (file: IFile) => void;
  onDelete: (fileId: string, parentId: string, fileName: string) => void;
}

const FileCardList = ({
  files,
  onNavigate,
  onRename,
  onDelete,
}: FileCardListProps) => {
  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className="w-full flex-1 overflow-auto min-h-[200px]">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {files.map(file => (
          <div key={file.id}>
            <FileCard
              file={file}
              onNavigate={onNavigate}
              onRename={onRename}
              onDelete={onDelete}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default FileCardList;
