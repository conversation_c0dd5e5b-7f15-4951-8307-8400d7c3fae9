'use client';

/**
 * 聊天引擎类型定义
 * 定义了事件总线、状态机、消息服务等核心类型
 */

// 聊天状态枚举
export enum ChatState {
  IDLE = 'idle',
  TYPING = 'typing',
  SENDING = 'sending',
  RECEIVING = 'receiving',
  STREAMING = 'streaming',
  ERROR = 'error',
  DISCONNECTED = 'disconnected',
}

// 消息类型
export interface ChatMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  isStreaming?: boolean;
  status?: 'pending' | 'sending' | 'sent' | 'delivered' | 'failed';
}

// 事件类型定义
export interface ChatEvent {
  type: string;
  payload?: unknown;
  timestamp: number;
  source?: string;
}

// 具体事件类型
export interface UserTypingEvent extends ChatEvent {
  type: 'USER_TYPING';
  payload: {
    content: string;
    conversationId: string;
  };
}

export interface UserSendMessageEvent extends ChatEvent {
  type: 'USER_SEND_MESSAGE';
  payload: {
    content: string;
    conversationId: string;
  };
}

export interface MessageSentEvent extends ChatEvent {
  type: 'MESSAGE_SENT';
  payload: {
    message: ChatMessage;
  };
}

export interface MessageReceivedEvent extends ChatEvent {
  type: 'MESSAGE_RECEIVED';
  payload: {
    message: ChatMessage;
  };
}

export interface StreamingStartEvent extends ChatEvent {
  type: 'STREAMING_START';
  payload: {
    messageId: string;
    conversationId: string;
  };
}

export interface StreamingContentEvent extends ChatEvent {
  type: 'STREAMING_CONTENT';
  payload: {
    messageId: string;
    delta: string;
    content: string;
  };
}

export interface StreamingEndEvent extends ChatEvent {
  type: 'STREAMING_END';
  payload: {
    messageId: string;
    finalContent: string;
  };
}

export interface ErrorEvent extends ChatEvent {
  type: 'ERROR';
  payload: {
    error: string;
    code?: string;
    conversationId?: string;
  };
}

// AGUI事件类型
export interface RunStartedEvent extends ChatEvent {
  type: 'RUN_STARTED';
  payload: {
    runId: string;
    conversationId?: string;
  };
}

export interface RunFinishedEvent extends ChatEvent {
  type: 'RUN_FINISHED';
  payload: {
    runId: string;
    conversationId?: string;
  };
}

export interface ToolCallStartEvent extends ChatEvent {
  type: 'TOOL_CALL_START';
  payload: {
    toolCallId: string;
    toolName: string;
    conversationId?: string;
  };
}

export interface ToolCallArgsEvent extends ChatEvent {
  type: 'TOOL_CALL_ARGS';
  payload: {
    toolCallId: string;
    args?: string;
    delta?: string;
    conversationId?: string;
  };
}

export interface ToolCallEndEvent extends ChatEvent {
  type: 'TOOL_CALL_END';
  payload: {
    toolCallId: string;
    finalArgs?: string;
    conversationId?: string;
  };
}

export interface ToolCallResultEvent extends ChatEvent {
  type: 'TOOL_CALL_RESULT';
  payload: {
    toolCallId: string;
    result?: any;
    conversationId?: string;
  };
}

export interface ConnectionStatusEvent extends ChatEvent {
  type: 'CONNECTION_STATUS';
  payload: {
    status: 'connected' | 'disconnected' | 'connecting' | 'reconnecting';
    conversationId?: string;
  };
}

export interface ConnectionStateChangedEvent extends ChatEvent {
  type: 'CONNECTION_STATE_CHANGED';
  payload: {
    connected: boolean;
  };
}

export interface ConversationSwitchedEvent extends ChatEvent {
  type: 'CONVERSATION_SWITCHED';
  payload: {
    conversationId: string;
  };
}

export interface LoadMessagesEvent extends ChatEvent {
  type: 'LOAD_MESSAGES';
  payload: {
    conversationId: string;
  };
}

export interface MessagesLoadedEvent extends ChatEvent {
  type: 'MESSAGES_LOADED';
  payload: {
    messages: ChatMessage[];
    conversationId: string;
  };
}

// 状态机状态
export interface ChatStateData {
  currentState: ChatState;
  conversationId: string | null;
  messages: ChatMessage[];
  currentInput: string;
  isConnected: boolean;
  error: string | null;
  streamingMessageId: string | null;
}

// 事件监听器类型
export type EventListener<T extends ChatEvent = ChatEvent> = (event: T) => void;

// 事件总线接口
export interface EventBus {
  emit<T extends ChatEvent>(event: T): void;
  on<T extends ChatEvent>(
    eventType: string,
    listener: EventListener<T>
  ): () => void;
  off<T extends ChatEvent>(eventType: string, listener: EventListener<T>): void;
  once<T extends ChatEvent>(
    eventType: string,
    listener: EventListener<T>
  ): void;
  removeAllListeners(eventType?: string): void;
  destroy(): void;
}

// 状态机接口
export interface ChatStateMachine {
  getCurrentState(): ChatState;
  getStateData(): ChatStateData;
  transition(event: ChatEvent): void;
  subscribe(listener: (state: ChatStateData) => void): () => void;
  reset(): void;
  setConversationId(conversationId: string): void;
  setError(error: string): void;
  clearError(): void;
  destroy(): void;
  // 新增方法：统一消息管理
  loadMessages(messages: ChatMessage[], conversationId?: string): void;
  getMessagesCount(): number;
}

// 消息服务接口
export interface MessageService {
  sendMessage(
    agentId: string,
    conversationId: string,
    content: string,
    tools?: any[]
  ): Promise<void>;
  loadMessages(conversationId: string): Promise<ChatMessage[]>;
  retryMessage(messageId: string): Promise<void>;
}

// 连接服务接口
export interface ConnectionService {
  connect(conversationId: string, agentId: string): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  getStatus(): 'connected' | 'disconnected' | 'connecting' | 'reconnecting';
  destroy(): Promise<void>;
}

// 聊天引擎配置
export interface ChatEngineConfig {
  conversationId: string;
  agentId: string;
  apiBaseUrl?: string;
  reconnectInterval?: number;
  maxRetries?: number;
  enableLogging?: boolean;
}

// 聊天引擎接口
export interface ChatEngine {
  initialize(config: ChatEngineConfig): Promise<void>;
  destroy(): Promise<void>;
  getEventBus(): EventBus;
  getStateMachine(): ChatStateMachine;
  getMessageService(): MessageService;
}
