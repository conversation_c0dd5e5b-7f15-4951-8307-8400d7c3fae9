'use client';

import { useHistoryStore } from '@/store/historyStore';
import { useCallback } from 'react';

export interface UseConversationsReturn {
  // 数据
  conversations: any[];
  groupedConversations: any[];
  selectedConversationId: string | null;
  selectedAgentId: string | null;

  // 状态
  loading: boolean;
  error: string | null;
  hasMore: boolean;

  // 操作状态
  isDeletingConversation: string | null;
  isRenamingConversation: string | null;

  // 方法
  loadConversations: (userId: string, refresh?: boolean) => Promise<void>;
  loadMore: (userId: string) => Promise<void>;
  addConversation: (conversation: any) => void;
  deleteConversation: (
    agentId: string,
    conversationId: string
  ) => Promise<boolean>;
  renameConversation: (
    agentId: string,
    conversationId: string,
    newTitle: string
  ) => Promise<boolean>;
  setSelectedConversation: (conversationId: string | null) => void;
  getConversationById: (conversationId: string) => any | null;
  clearError: () => void;
}

export const useConversations = (): UseConversationsReturn => {
  // 从store获取状态
  const conversations = useHistoryStore(state => state.conversations);
  const selectedConversationId = useHistoryStore(
    state => state.selectedConversationId
  );
  const selectedAgentId = useHistoryStore(state => state.selectedAgentId);
  const loading = useHistoryStore(state => state.loading);
  const error = useHistoryStore(state => state.error);
  const hasMore = useHistoryStore(state => state.hasMore);
  const isDeletingConversation = useHistoryStore(
    state => state.isDeletingConversation
  );
  const isRenamingConversation = useHistoryStore(
    state => state.isRenamingConversation
  );

  // 从store获取方法
  const loadConversationsAction = useHistoryStore(
    state => state.loadConversations
  );
  const loadMoreConversationsAction = useHistoryStore(
    state => state.loadMoreConversations
  );
  const addConversationAction = useHistoryStore(state => state.addConversation);
  const deleteConversationAction = useHistoryStore(
    state => state.deleteConversation
  );
  const renameConversationAction = useHistoryStore(
    state => state.renameConversation
  );
  const setSelectedConversationAction = useHistoryStore(
    state => state.setSelectedConversation
  );
  const getGroupedConversationsAction = useHistoryStore(
    state => state.getGroupedConversations
  );
  const getConversationByIdAction = useHistoryStore(
    state => state.getConversationById
  );
  const setError = useHistoryStore(state => state.setError);

  // 包装方法以提供更好的类型安全和错误处理
  const loadConversations = useCallback(
    async (userId: string, refresh = false) => {
      try {
        await loadConversationsAction(userId, refresh);
      } catch (error) {
        console.error('加载会话列表失败:', error);
        throw error;
      }
    },
    [loadConversationsAction]
  );

  const loadMore = useCallback(
    async (userId: string) => {
      try {
        await loadMoreConversationsAction(userId);
      } catch (error) {
        console.error('加载更多会话失败:', error);
        throw error;
      }
    },
    [loadMoreConversationsAction]
  );

  const addConversation = useCallback(
    (conversation: any) => {
      addConversationAction(conversation);
    },
    [addConversationAction]
  );

  const deleteConversation = useCallback(
    async (agentId: string, conversationId: string) => {
      try {
        return await deleteConversationAction(agentId, conversationId);
      } catch (error) {
        console.error('删除会话失败:', error);
        throw error;
      }
    },
    [deleteConversationAction]
  );

  const renameConversation = useCallback(
    async (agentId: string, conversationId: string, newTitle: string) => {
      try {
        return await renameConversationAction(
          agentId,
          conversationId,
          newTitle
        );
      } catch (error) {
        console.error('重命名会话失败:', error);
        throw error;
      }
    },
    [renameConversationAction]
  );

  const setSelectedConversation = useCallback(
    (conversationId: string | null) => {
      setSelectedConversationAction(conversationId);
    },
    [setSelectedConversationAction]
  );

  const getConversationById = useCallback(
    (conversationId: string) => {
      return getConversationByIdAction(conversationId);
    },
    [getConversationByIdAction]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  // 获取分组的会话列表
  const groupedConversations = getGroupedConversationsAction();

  return {
    // 数据
    conversations,
    groupedConversations,
    selectedConversationId,
    selectedAgentId,

    // 状态
    loading,
    error,
    hasMore,

    // 操作状态
    isDeletingConversation,
    isRenamingConversation,

    // 方法
    loadConversations,
    loadMore,
    addConversation,
    deleteConversation,
    renameConversation,
    setSelectedConversation,
    getConversationById,
    clearError,
  };
};
