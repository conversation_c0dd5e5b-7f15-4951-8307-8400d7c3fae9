import React from 'react';
import { formatDateTime } from '@/utils/data-transform';

interface CardBoxProps {
  employee: any;
  onStart?: () => void; // 新增 onStart props
}

export default function CardBox({ employee, onStart }: CardBoxProps) {
  return (
    <div
      className={
        'bg-[#00000005] rounded-[8px] py-3 px-4 flex flex-col max-w-[400px] lg:max-w-[600px] relative group'
      }
    >
      {/* 右上角开始按钮 */}
      <button
        className="flex items-center justify-center h-[37px] cursor-pointer absolute top-3 right-3 px-2 py-3 bg-[#000000E0] hover:bg-[#000000F0] text-white text-[14px] rounded-[8px]  transition-opacity opacity-0 group-hover:opacity-100 focus:outline-none"
        style={{ zIndex: 1 }}
        onClick={onStart} // 绑定 onClick
      >
        <img src="/start.svg" alt="start" className="mr-2" />
        开始
      </button>
      {/* 标签区域 */}
      {employee.tags && employee.tags.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-1">
          {employee.tags.map((tag: string, idx: number) => (
            <span
              key={idx}
              className="inline-block bg-[#0000000A] text-xs text-[#00000066] px-[6px] py-[2px] rounded-[4px] text-[12px]"
            >
              {tag}
            </span>
          ))}
        </div>
      )}
      <div className="text-[16px] font-medium text-[#000000E0] mb-[6px] truncate">
        {employee?.name}
      </div>
      <div className="text-[13px] text-[#000000B8] mb-[6px] truncate">
        {employee.description}
      </div>
      <div className="flex items-center text-[12px] text-[#00000066] gap-x-1 overflow-hidden">
        <span className="truncate">@{employee.user?.user_name}</span>
        <span className="mx-1">|</span>
        <span className="truncate">
          {formatDateTime(employee.user?.create_date)}
        </span>
      </div>
    </div>
  );
}
