import { ResponseType } from '@/interfaces/database/base';
import { IFolder } from '@/interfaces/database/file-manager';
import { IConnectRequestBody } from '@/interfaces/request/file-manager';
import fileManagerService from '@/services/file-manager-service';
import { downloadFileFromBlob } from '@/utils/file-util';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PaginationProps, UploadFile, message } from 'antd';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'next/navigation';
import {
  useGetPaginationWithRouter,
  useHandleSearchChange,
} from './logic-hooks';
import { useSetPaginationParams } from './route-hook';

// 使用 Next.js 的 useSearchParams
export const useGetFolderId = () => {
  const searchParams = useSearchParams();
  const id = searchParams.get('folderId') as string;

  return id ?? '';
};

export interface IListResult {
  searchString: string;
  handleInputChange: React.ChangeEventHandler<HTMLInputElement>;
  pagination: PaginationProps;
  setPagination: (pagination: { page: number; pageSize: number }) => void;
  loading: boolean;
  refetch?: () => void; // 添加可选的 refetch 方法
}

// 修改返回类型定义，确保包含筛选相关字段
export interface IListResultV2 extends IListResult {
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  selectedRole: string;
  setSelectedRole: (role: string) => void;
  desc: boolean;
  onSortDirectionChange: (descending: boolean) => void;
}

export const useFetchPureFileList = () => {
  const { mutateAsync, isPending: loading } = useMutation({
    mutationFn: async (parentId: string) => {
      const { data } = await fileManagerService.listFile({
        parent_id: parentId,
      });

      return data;
    },
    mutationKey: ['fetchPureFileList'],
    gcTime: 0,
  });

  return { loading, fetchList: mutateAsync };
};

export const useFetchFileList = (): IListResultV2 & { data: any } => {
  const { searchString, handleInputChange } = useHandleSearchChange();
  const id = useGetFolderId();

  // 使用本地状态管理分页，避免路由复杂性
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(100);

  // 新增筛选相关状态
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [desc, setDesc] = useState<boolean>(true);

  // 调试信息
  // console.log('🔍 useFetchFileList 调试信息:', {
  //   id,
  //   searchString,
  //   currentPage,
  //   pageSize,
  // });

  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery({
    queryKey: [
      'fetchFileList',
      id || '', // 确保不是 undefined
      searchString || '', // 确保不是 undefined
      currentPage,
      pageSize,
      selectedTags,
      selectedRole,
      desc,
    ],
    queryFn: async () => {
      console.log('📡 发起文件列表请求:', {
        parent_id: id || '',
        keywords: searchString || '',
        tags: selectedTags.length > 0 ? selectedTags.join(',') : undefined,
        role: selectedRole,
        orderby: 'create_time',
        desc,
        page_size: pageSize,
        page: currentPage,
      });

      const { data } = await fileManagerService.listFile({
        parent_id: id || '',
        keywords: searchString || '',
        tags: selectedTags.length > 0 ? selectedTags.join(',') : undefined,
        role: selectedRole,
        orderby: 'create_time',
        desc,
        page_size: pageSize,
        page: currentPage,
      });

      console.log('✅ 文件列表响应:', data);
      return data;
    },
    enabled: true, // 始终启用查询
    refetchOnMount: true, // 组件挂载时重新获取
    refetchOnWindowFocus: false, // 防止频繁刷新
  });

  // 分页配置
  const pagination = useMemo(
    () => ({
      current: currentPage,
      pageSize: pageSize,
      total: data?.data?.total || 0,
      showQuickJumper: true,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      onChange: (page: number, size: number) => {
        console.log('📄 分页变化:', { page, size });
        setCurrentPage(page);
        if (size !== pageSize) {
          setPageSize(size);
          setCurrentPage(1); // 改变页面大小时重置到第一页
        }
      },
      showTotal: (total: number) => `共 ${total} 条记录`,
    }),
    [currentPage, pageSize, data?.data?.total]
  );

  const setPagination = useCallback(
    (params: { page: number; pageSize?: number }) => {
      console.log('🔄 设置分页:', params);
      setCurrentPage(params.page);
      if (params.pageSize && params.pageSize !== pageSize) {
        setPageSize(params.pageSize);
      }
    },
    [pageSize]
  );

  const onInputChange: React.ChangeEventHandler<HTMLInputElement> = useCallback(
    e => {
      handleInputChange(e);
      setCurrentPage(1); // 搜索时重置到第一页
    },
    [handleInputChange]
  );

  // tag筛选
  const onTagsChange = useCallback((tags: string[]) => {
    setCurrentPage(1);
    setSelectedTags(tags);
  }, []);

  // 角色筛选
  const onRoleChange = useCallback((role: string) => {
    setCurrentPage(1);
    setSelectedRole(role);
  }, []);

  // 排序方式
  const onSortDirectionChange = useCallback((descending: boolean) => {
    setCurrentPage(1);
    setDesc(descending);
  }, []);

  return {
    data: data?.data || { files: [], total: 0 },
    searchString,
    handleInputChange: onInputChange,
    pagination,
    setPagination,
    loading,
    refetch, // 暴露手动刷新功能
    selectedTags,
    setSelectedTags: onTagsChange,
    selectedRole,
    setSelectedRole: onRoleChange,
    desc,
    onSortDirectionChange,
  };
};

export const useDeleteFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: { fileIds: string[]; parentId: string }) => {
      const { data } = await fileManagerService.removeFile(params);
      if (data.code === 0) {
        setPaginationParams(1);
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['deleteFile'],
  });

  return { data, loading, deleteFile: mutateAsync };
};

export const useRenameFile = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: { fileId: string; name: string }) => {
      const { data } = await fileManagerService.renameFile(params);
      if (data.code === 0) {
        message.success(t('message.renamed'));
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['renameFile'],
  });

  return { data, loading, renameFile: mutateAsync };
};

export const useEditFile = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: {
      fileId: string;
      name: string;
      description?: string;
      tags?: string[];
    }) => {
      const { data } = await fileManagerService.editFile(params);
      if (data.code === 0) {
        message.success(t('message.edited'));
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['editFile'],
  });

  return { data, loading, editFile: mutateAsync };
};

export const useCreateFolder = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: {
      parentId: string;
      name: string;
      description?: string;
      tags?: string[];
    }) => {
      const { data } = await fileManagerService.createFolder({
        ...params,
        type: 'folder',
      });
      if (data.code === 0) {
        message.success(t('message.created'));
        setPaginationParams(1);
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['createFolder'],
  });

  return { data, loading, createFolder: mutateAsync };
};

export const useUploadFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: {
      fileList: UploadFile[];
      parentId: string;
    }) => {
      const fileList = params.fileList;
      const pathList = params.fileList.map(
        file => (file as any).webkitRelativePath
      );
      const formData = new FormData();
      formData.append('parent_id', params.parentId);
      fileList.forEach((file: any, index: number) => {
        formData.append('file', file);
        formData.append('path', pathList[index]);
      });
      try {
        const ret = await fileManagerService.uploadFile(formData);
        if (ret?.data.code === 0) {
          message.success(t('message.uploaded'));
          setPaginationParams(1);
          queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
        }
        return ret?.data?.code;
      } catch (error) {
        console.log('🚀 ~ useUploadFile ~ error:', error);
      }
    },
    mutationKey: ['uploadFile'],
  });

  return { data, loading, uploadFile: mutateAsync };
};

export const useConnectToKnowledge = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: IConnectRequestBody) => {
      const { data } = await fileManagerService.connectFileToKnowledge(params);
      if (data.code === 0) {
        message.success(t('message.operated'));
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['connectFileToKnowledge'],
  });

  return { data, loading, connectFileToKnowledge: mutateAsync };
};

export interface IMoveFileBody {
  src_file_ids: string[];
  dest_file_id: string; // target folder id
}

export const useMoveFile = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: IMoveFileBody) => {
      const { data } = await fileManagerService.moveFile(params);
      if (data.code === 0) {
        message.success(t('message.operated'));
        queryClient.invalidateQueries({ queryKey: ['fetchFileList'] });
      }
      return data.code;
    },
    mutationKey: ['moveFile'],
  });

  return { data, loading, moveFile: mutateAsync };
};

export const useDownloadFile = () => {
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationFn: async (params: { id: string; filename?: string }) => {
      const response = await fileManagerService.getFile({}, params.id);
      const blob = new Blob([response.data], { type: response.data.type });
      downloadFileFromBlob(blob, params.filename);
    },
    mutationKey: ['downloadFile'],
  });
  return { data, loading, downloadFile: mutateAsync };
};

export const useFetchParentFolderList = (): IFolder[] => {
  const id = useGetFolderId();

  const { data, error, isLoading, isError } = useQuery({
    queryKey: ['fetchParentFolderList', id],
    queryFn: async () => {
      // 如果没有id，表示在根目录，返回根目录信息
      if (!id) {
        return [
          {
            id: '',
            name: '/',
            type: 'folder',
            source_type: 'file_manager',
          },
        ];
      }

      try {
        const response = await fileManagerService.getAllParentFolder({
          fileId: id,
        });

        // 尝试不同的数据路径
        let parentFolders = [];
        if (response?.data?.data?.parent_folders) {
          parentFolders = response.data.data.parent_folders.toReversed();
        } else if (response?.data?.parent_folders) {
          parentFolders = response.data.parent_folders.toReversed();
        } else {
          console.log('⚠️ 未找到 parent_folders 数据');
        }

        console.log('📁 处理后的父文件夹列表:', parentFolders);

        return parentFolders;
      } catch (err) {
        console.error('❌ 获取父文件夹失败:', err);
        throw err;
      }
    },
    initialData: [],
    enabled: true, // 始终启用查询
    retry: 1,
    staleTime: 0, // 确保每次都重新获取
  });
  return data;
};

// 获取所有文件标签
export const useFetchFileTags = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['fetchFileTags'],
    queryFn: async () => {
      const { data } = await fileManagerService.getFileTags();
      return data?.data?.tags || [];
    },
  });

  return { tags: data, loading: isLoading };
};
