import { useTranslate } from '@/hooks/common-hooks';
import {
  IListResult,
  useFetchParentFolderList,
  useFetchFileTags,
  IListResultV2,
} from '@/hooks/file-manager-hooks';
import {
  DownOutlined,
  FileTextOutlined,
  FolderOpenOutlined,
  PlusOutlined,
  SearchOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import {
  Breadcrumb,
  BreadcrumbProps,
  Button,
  Dropdown,
  Flex,
  Input,
  MenuProps,
  Space,
  Select,
} from 'antd';
import { useCallback, useMemo } from 'react';
import {
  useHandleBreadcrumbClick,
  useHandleDeleteFile,
  useSelectBreadcrumbItems,
} from './hooks';
import { useGetFolderId } from '@/hooks/file-manager-hooks';

import { FolderInput, Trash2 } from 'lucide-react';

interface IProps extends IListResultV2 {
  selectedRowKeys: string[];
  showFolderCreateModal: () => void;
  showFileUploadModal: () => void;
  setSelectedRowKeys: (keys: string[]) => void;
  showMoveFileModal: (ids: string[]) => void;
  total?: number;
  isBatchMode: boolean;
  onEnterBatchMode: () => void;
  onExitBatchMode: () => void;
  dataSource: { id: string; name: string }[]; // 新增参数，传递当前所有可见文件/文件夹列表
}

const FileToolbar = ({
  selectedRowKeys,
  showFolderCreateModal,
  showFileUploadModal,
  setSelectedRowKeys,
  searchString,
  handleInputChange,
  showMoveFileModal,
  total = 0,
  isBatchMode,
  onEnterBatchMode,
  onExitBatchMode,
  dataSource,
  selectedTags,
  setSelectedTags,
  selectedRole,
  setSelectedRole,
  desc,
  onSortDirectionChange,
}: IProps) => {
  const { t } = useTranslate('knowledgeDetails');
  const breadcrumbItems = useSelectBreadcrumbItems();
  const { handleBreadcrumbClick } = useHandleBreadcrumbClick();
  const parentFolderList = useFetchParentFolderList();
  const currentFolderId = useGetFolderId();
  const isKnowledgeBase =
    parentFolderList.at(-1)?.source_type === 'knowledgebase';

  // 判断是否为根目录（主页）
  const isRootDirectory = !currentFolderId;

  // 返回主页
  const handleBackToHome = useCallback(() => {
    handleBreadcrumbClick('/files');
  }, [handleBreadcrumbClick]);

  const itemRender: BreadcrumbProps['itemRender'] = (
    currentRoute,
    params,
    items
  ) => {
    const isLast = currentRoute?.path === items[items.length - 1]?.path;

    return isLast ? (
      <span>{currentRoute.title}</span>
    ) : (
      <span
        className="breadcrumb-item-button"
        onClick={() => handleBreadcrumbClick(currentRoute.path)}
      >
        {currentRoute.title}
      </span>
    );
  };

  const actionItems: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '1',
        onClick: showFileUploadModal,
        label: (
          <div>
            <Button type="link">
              <Space>
                <FileTextOutlined />
                {t('uploadFile', { keyPrefix: 'fileManager' })}
              </Space>
            </Button>
          </div>
        ),
      },
      { type: 'divider' },
      {
        key: '2',
        onClick: showFolderCreateModal,
        label: (
          <div>
            <Button type="link">
              <Space>
                <FolderOpenOutlined />
                {t('newFolder', { keyPrefix: 'fileManager' })}
              </Space>
            </Button>
          </div>
        ),
      },
    ];
  }, [t, showFolderCreateModal, showFileUploadModal]);

  // 传递所有可见文件/文件夹，确保批量删除时能显示所有选中项名称
  const { handleRemoveFile } = useHandleDeleteFile(
    selectedRowKeys,
    setSelectedRowKeys,
    dataSource,
    isBatchMode ? onExitBatchMode : undefined // 只在批量模式下传递
  );

  const handleShowMoveFileModal = useCallback(() => {
    showMoveFileModal(selectedRowKeys);
  }, [selectedRowKeys, showMoveFileModal]);

  const disabled = selectedRowKeys.length === 0;

  const items: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '4',
        onClick: handleRemoveFile,
        label: (
          <Flex gap={10}>
            <span className="flex items-center justify-center">
              <Trash2 className="size-4" />
            </span>
            <b>{t('delete', { keyPrefix: 'common' })}</b>
          </Flex>
        ),
      },
      {
        key: '5',
        onClick: handleShowMoveFileModal,
        label: (
          <Flex gap={10}>
            <span className="flex items-center justify-center">
              <FolderInput className="size-4"></FolderInput>
            </span>
            <b>{t('move', { keyPrefix: 'common' })}</b>
          </Flex>
        ),
      },
    ];
  }, [handleShowMoveFileModal, t, handleRemoveFile]);

  // 获取标签选项
  const { tags: tagOptions, loading: tagsLoading } = useFetchFileTags();

  // 批量操作模式时，显示批量操作栏
  if (isBatchMode) {
    return (
      <div className="filter">
        <Space>
          <Breadcrumb
            items={breadcrumbItems}
            itemRender={itemRender}
            style={{ visibility: 'hidden' }}
          />
        </Space>
        <Space>
          <span
            style={{
              color: '#454545',
              fontSize: '14px',
              backgroundColor: '#f7f7f7',
              padding: '8px 16px',
              borderRadius: '8px',
              height: '40px',
            }}
          >
            {selectedRowKeys.length > 0
              ? `已选择 ${selectedRowKeys.length} 项`
              : '请选择要操作的文件'}
          </span>
          <Button
            onClick={handleRemoveFile}
            className="file-toolbar-delete-button"
            disabled={selectedRowKeys.length === 0}
            style={{
              backgroundColor: '#ffffff',
              color: '#e74639',
              border: '1px solid #fce8e7',
              borderRadius: '8px',
            }}
          >
            删除
          </Button>
          <Button
            onClick={onExitBatchMode}
            className="file-toolbar-cancel-button"
            style={{
              backgroundColor: '#ffffff',
              color: '#464646',
              border: '1px solid #ebebeb',
              borderRadius: '8px',
            }}
          >
            取消
          </Button>
        </Space>
      </div>
    );
  }

  return (
    <div className="filter">
      <Space>
        {/* 在二级文件夹中显示返回按钮 */}
        {!isRootDirectory && (
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToHome}
            className="file-toolbar-back-button"
            style={{
              border: 'none',
              marginRight: '10px',
              color: '#000',
              backgroundColor: '#fafafa',
            }}
          ></Button>
        )}

        {/* 主页显示创建文件组按钮 */}
        {isRootDirectory && (
          <Space>
            <Button
              type="primary"
              onClick={showFolderCreateModal}
              className="root-create-folder-button"
              style={{
                backgroundColor: 'black',
                color: '#fff',
                border: 'none',
              }}
            >
              <span className="flex items-center">
                <img
                  src="/assets/files/IconCreate2.svg"
                  alt="创建"
                  className="w-4 h-4 mr-2"
                />
                创建文件组
              </span>
            </Button>
            {/* 分隔线 */}
            <div
              style={{
                width: '1px',
                height: '24px',
                backgroundColor: '#ebebeb',
                margin: '0 8px',
              }}
            />
            <Input
              placeholder="搜索"
              value={searchString}
              style={{
                width: 220,
                border: 'none',
                backgroundColor: '#f2f2f2',
                borderRadius: '10px',
                padding: '8px 12px',
              }}
              allowClear
              onChange={handleInputChange}
              prefix={<SearchOutlined />}
              className="file-toolbar-search-input"
            />
          </Space>
        )}

        {/* 只在二级文件夹中显示上传和创建按钮 */}
        {!isRootDirectory &&
          (isKnowledgeBase || (
            <>
              <Button
                type="primary"
                onClick={showFileUploadModal}
                className="file-toolbar-upload-button"
                style={{
                  backgroundColor: 'black',
                  color: '#fff',
                  border: 'none',
                }}
              >
                <span className="flex items-center">
                  <img
                    src="/assets/files/IconUploadFile.svg"
                    alt="上传"
                    className="w-4 h-4 mr-2"
                  />
                  上传文件
                </span>
              </Button>
              <Button
                onClick={showFolderCreateModal}
                className="file-toolbar-create-folder-button"
                style={{
                  backgroundColor: '#ffffff',
                  color: '#464646',
                  border: '1px solid #ebebeb',
                  borderRadius: '10px',
                  padding: '8px 12px',
                }}
              >
                <span className="flex items-center">
                  <img
                    src="/assets/files/IconCreate.svg"
                    alt="创建"
                    className="w-4 h-4 mr-2"
                  />
                  创建文件组
                </span>
              </Button>
              {/* 分隔线 */}
              <div
                style={{
                  width: '1px',
                  height: '24px',
                  backgroundColor: '#ebebeb',
                  margin: '0 8px',
                }}
              />
              <Input
                placeholder="AI搜索..."
                value={searchString}
                style={{
                  width: 220,
                  border: 'none',
                  backgroundColor: '#f2f2f2',
                  borderRadius: '10px',
                  padding: '0 10px',
                }}
                allowClear
                onChange={handleInputChange}
                prefix={<SearchOutlined />}
                className="file-toolbar-search-input"
              />
            </>
          ))}
        {!isRootDirectory && (
          <Breadcrumb
            items={breadcrumbItems}
            itemRender={itemRender}
            style={{ display: 'none' }}
          />
        )}
        {/* 角色筛选 */}
        {/* <Select
            placeholder="筛选角色"
            style={{ width: 120 }}
            allowClear
            value={selectedRole}
            onChange={(role: string) => setSelectedRole(role)}
            options={[
              { label: '全部', value: 'all' },
              { label: '系统', value: 'admin' },
              { label: '用户', value: 'user' },
            ]}
          /> */}
      </Space>
      <Space>
        <span
          style={{ color: '#adadad', fontSize: '14px', visibility: 'hidden' }}
        >
          共 {total} 个文件
        </span>
        {isRootDirectory && (
          <Select
            mode="multiple"
            placeholder="标签"
            style={{ width: 180 }}
            allowClear
            value={selectedTags}
            onChange={(tags: string[]) => setSelectedTags(tags)}
            loading={tagsLoading}
            options={tagOptions?.map((tag: string) => ({
              label: tag,
              value: tag,
            }))}
            maxTagCount="responsive"
          />
        )}
        {/* 只在二级文件夹中显示排序和批量操作按钮 */}
        {!isRootDirectory &&
          (isKnowledgeBase || (
            <>
              {/* 排序按钮 */}
              <Button
                onClick={() => onSortDirectionChange(!desc)}
                title={desc ? '最新优先' : '最旧优先'}
                style={{
                  backgroundColor: '#fafafa',
                  color: '#464646',
                  border: '1px solid #ebebeb',
                  borderRadius: '8px',
                  height: '40px',
                }}
              >
                {desc ? '↓' : '↑'} 按创建时间排序
              </Button>
              {/* 批量操作按钮 */}
              <Button
                onClick={onEnterBatchMode}
                className="file-toolbar-batch-button"
                style={{
                  backgroundColor: '#ffffff',
                  color: '#464646',
                  border: '1px solid #ebebeb',
                  borderRadius: '8px',
                }}
              >
                批量操作
              </Button>
            </>
          ))}
      </Space>
    </div>
  );
};

export default FileToolbar;
