'use client';

import { useAuth } from '@/lib/auth/AuthProvider';
import { useMessageStore } from '@/store/messageStore';
import { useCallback, useEffect, useMemo } from 'react';

// 与ToolCallStatus组件保持一致的接口定义
interface ToolCallInfo {
  id: string;
  name: string;
  args: string;
  status: 'calling' | 'completed';
}

/**
 * useChatMessages - 消息管理hook
 * 直接使用 messageStore，集成 WebSocket 管理，确保全局状态共享
 */
export const useChatMessages = (conversationId?: string) => {
  const { token } = useAuth();

  // 从 store 获取状态
  const messages = useMessageStore(state => state.messages);
  const loading = useMessageStore(state => state.loading);
  const error = useMessageStore(state => state.error);
  const isStreaming = useMessageStore(state => state.isStreaming);
  const streamingMessageId = useMessageStore(state => state.streamingMessageId);
  // const activeToolCalls = useMessageStore(state => state.activeToolCalls);
  const connected = useMessageStore(state => state.connected);
  const isRunning = useMessageStore(state => state.isRunning);
  const currentConversationId = useMessageStore(
    state => state.currentConversationId
  );

  // 从 store 获取操作方法
  const loadMessages = useMessageStore(state => state.loadMessages);
  const sendMessage = useMessageStore(state => state.sendMessage);
  const clearMessages = useMessageStore(state => state.clearMessages);
  const clearError = useMessageStore(state => state.clearError);
  const setError = useMessageStore(state => state.setError);
  const initializeWebSocket = useMessageStore(
    state => state.initializeWebSocket
  );
  const connectToConversation = useMessageStore(
    state => state.connectToConversation
  );
  const disconnectWebSocket = useMessageStore(
    state => state.disconnectWebSocket
  );

  // 初始化 WebSocket（全局唯一，多个组件调用也只会初始化一次）
  useEffect(() => {
    initializeWebSocket(token || undefined);
  }, [token, initializeWebSocket]);

  // 当会话ID变化时，连接到对应会话并清空旧消息
  useEffect(() => {
    if (conversationId && conversationId !== currentConversationId) {
      console.log('[useChatMessages] 会话切换:', {
        from: currentConversationId,
        to: conversationId,
      });

      // 清空旧消息，避免显示错误的历史记录
      if (currentConversationId !== null) {
        clearMessages();
      }

      // 连接到新会话
      connectToConversation(conversationId, token || undefined);
    }
  }, [
    conversationId,
    currentConversationId,
    token,
    connectToConversation,
    clearMessages,
  ]);

  // 加载消息的统一方法
  const loadMessagesForConversation = useCallback(
    async (targetConversationId?: string) => {
      const conversationIdToLoad = targetConversationId || conversationId;
      if (!conversationIdToLoad) {
        console.warn('[useChatMessages] 无法加载消息：缺少会话ID');
        return;
      }

      try {
        // 先连接到会话
        await connectToConversation(conversationIdToLoad, token || undefined);

        // 加载历史消息
        await loadMessages(conversationIdToLoad);

        console.log('[useChatMessages] 消息加载完成:', conversationIdToLoad);
      } catch (error) {
        console.error('[useChatMessages] 加载消息失败:', error);
        setError(`加载消息失败: ${error}`);
      }
    },
    [conversationId, token, connectToConversation, loadMessages, setError]
  );

  // 发送消息的统一方法
  const sendMessageToAgent = useCallback(
    async (content: string, agentId?: string) => {
      if (!conversationId || !content.trim()) {
        console.warn('[useChatMessages] 无法发送消息：缺少会话ID或内容为空');
        return;
      }

      if (!agentId) {
        console.warn('[useChatMessages] 无法发送消息：缺少agentId');
        return;
      }

      try {
        await sendMessage(agentId, content);
      } catch (error) {
        console.error('[useChatMessages] 发送消息失败:', error);
        throw error;
      }
    },
    [conversationId, sendMessage]
  );

  // 重试发送消息
  const retryMessage = useCallback(async (messageId: string) => {
    // TODO: 实现消息重试逻辑
    console.log('[useChatMessages] 重试消息:', messageId);
  }, []);

  // 派生状态计算 - 将 ToolCallExecution 转换为 ToolCallInfo
  const derivedState = useMemo(() => {
    const convertedToolCalls = new Map<string, ToolCallInfo>();
    const convertedToolCallsArray: ToolCallInfo[] = [];

    // activeToolCalls.forEach((execution, id) => {
    //   const toolCallInfo: ToolCallInfo = {
    //     id: execution.id,
    //     name: execution.name,
    //     args: execution.args,
    //     status: execution.status === 'completed' ? 'completed' : 'calling',
    //   };
    //   convertedToolCalls.set(id, toolCallInfo);
    //   convertedToolCallsArray.push(toolCallInfo);
    // });

    return {
      isEmpty: messages.length === 0,
      hasError: error !== null,
      canSend:
        !loading && !isStreaming && !isRunning && connected && conversationId,
      // hasActiveToolCalls: activeToolCalls.size > 0,
      toolCallsArray: convertedToolCallsArray,
      activeToolCallsForUI: convertedToolCalls,
    };
  }, [
    messages.length,
    error,
    loading,
    isStreaming,
    isRunning,
    connected,
    conversationId,
  ]);

  return {
    // 基础数据
    messages,
    loading,
    error,
    isStreaming,
    streamingMessageId,
    currentConversationId: conversationId || null,

    // AGUI状态
    isRunning,
    activeToolCalls: derivedState.activeToolCallsForUI,
    connected,

    // 方法
    loadMessages: loadMessagesForConversation,
    sendMessage: sendMessageToAgent,
    clearMessages,
    clearError,
    retryMessage,
    disconnectWebSocket,

    // 派生状态
    isEmpty: derivedState.isEmpty,
    hasError: derivedState.hasError,
    canSend: derivedState.canSend,
    // hasActiveToolCalls: derivedState.hasActiveToolCalls,
    toolCallsArray: derivedState.toolCallsArray,
  };
};
