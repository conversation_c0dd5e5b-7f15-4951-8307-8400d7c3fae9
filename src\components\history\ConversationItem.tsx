'use client';

import React, { useCallback, useState } from 'react';
import { ConversationItem as ConversationItemType } from '@/types/conversation';
import ConversationContextMenu from './ConversationContextMenu';

interface ConversationItemProps {
  conversation: ConversationItemType;
  isSelected: boolean;
  isDeleting: boolean;
  isRenaming: boolean;
  onClick: () => void;
  onDelete: () => Promise<void>;
  onRename: (newTitle: string) => Promise<void>;
}

export default function ConversationItem({
  conversation,
  isSelected,
  isDeleting,
  isRenaming,
  onClick,
  onDelete,
  onRename,
}: ConversationItemProps) {
  // 上下文菜单状态
  const [contextMenu, setContextMenu] = useState<{
    isVisible: boolean;
    position: { x: number; y: number };
  }>({
    isVisible: false,
    position: { x: 0, y: 0 },
  });

  // 内联编辑状态
  const [editingTitle, setEditingTitle] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // 显示上下文菜单
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setContextMenu({
      isVisible: true,
      position: { x: e.clientX, y: e.clientY },
    });
  }, []);

  // 三点按钮点击
  const handleMenuButtonClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const rect = (e.target as HTMLElement).getBoundingClientRect();
    setContextMenu({
      isVisible: true,
      position: { x: rect.left - 160, y: rect.bottom + 4 }, // 菜单右对齐
    });
  }, []);

  // 关闭上下文菜单
  const handleCloseContextMenu = useCallback(() => {
    setContextMenu({
      isVisible: false,
      position: { x: 0, y: 0 },
    });
  }, []);

  // 删除会话
  const handleDeleteConversation = useCallback(async () => {
    if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
      handleCloseContextMenu();
      return;
    }

    try {
      await onDelete();
      handleCloseContextMenu();
    } catch (error) {
      console.error('删除会话失败:', error);
      handleCloseContextMenu();
    }
  }, [onDelete, handleCloseContextMenu]);

  // 开始内联编辑
  const handleStartInlineEdit = useCallback(() => {
    setEditingTitle(conversation.titleAlias || conversation.title);
    setIsEditing(true);
    handleCloseContextMenu();
  }, [conversation.titleAlias, conversation.title, handleCloseContextMenu]);

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    if (!editingTitle.trim()) {
      setIsEditing(false);
      return;
    }

    const currentTitle = conversation.titleAlias || conversation.title;
    if (editingTitle.trim() === currentTitle) {
      setIsEditing(false);
      return;
    }

    try {
      await onRename(editingTitle.trim());
      setIsEditing(false);
    } catch (error) {
      console.error('重命名会话失败:', error);
      setIsEditing(false);
    }
  }, [editingTitle, conversation.titleAlias, conversation.title, onRename]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
  }, []);

  // 处理输入框键盘事件
  const handleEditKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        handleSaveEdit();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        handleCancelEdit();
      }
    },
    [handleSaveEdit, handleCancelEdit]
  );

  return (
    <>
      <div
        className={`group flex flex-row justify-between items-center px-2.5 py-1.5 gap-3 w-66 h-9 rounded-lg cursor-pointer transition-colors ${
          isSelected ? 'bg-layer-3' : 'hover:bg-layer-2'
        } ${isDeleting ? 'opacity-50' : ''} ${isEditing ? 'cursor-default' : ''}`}
        onClick={() => !isDeleting && !isRenaming && !isEditing && onClick()}
        onContextMenu={e => !isEditing && handleContextMenu(e)}
      >
        {/* 内容区域：编辑时显示输入框，否则显示文本 */}
        {isEditing ? (
          <input
            ref={input => {
              if (input) {
                input.focus();
                input.select();
              }
            }}
            type="text"
            value={editingTitle}
            onChange={e => setEditingTitle(e.target.value)}
            onKeyDown={handleEditKeyDown}
            onBlur={handleSaveEdit}
            className="flex-1 text-sm font-normal leading-5 text-text-primary bg-transparent border-none outline-none"
            placeholder="输入会话标题"
          />
        ) : (
          <span className="flex-1 text-sm font-normal leading-5 text-text-primary line-clamp-1">
            {isRenaming
              ? '重命名中...'
              : conversation.titleAlias || conversation.title}
          </span>
        )}

        {/* 三点菜单按钮 - 编辑时隐藏 */}
        {!isEditing && (
          <button
            onClick={handleMenuButtonClick}
            disabled={isRenaming || isDeleting}
            className={`flex justify-center items-center w-6 h-6 p-1 bg-layer-3 rounded-md transition-opacity ${
              isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
            }`}
            title="更多操作"
          >
            <div className="w-4 h-4">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M8 8.66667C8.36819 8.66667 8.66667 8.36819 8.66667 8C8.66667 7.63181 8.36819 7.33333 8 7.33333C7.63181 7.33333 7.33333 7.63181 7.33333 8C7.33333 8.36819 7.63181 8.66667 8 8.66667Z"
                  fill="rgba(0, 0, 0, 0.72)"
                />
                <path
                  d="M8 4C8.36819 4 8.66667 3.70152 8.66667 3.33333C8.66667 2.96514 8.36819 2.66667 8 2.66667C7.63181 2.66667 7.33333 2.96514 7.33333 3.33333C7.33333 3.70152 7.63181 4 8 4Z"
                  fill="rgba(0, 0, 0, 0.72)"
                />
                <path
                  d="M8 13.3333C8.36819 13.3333 8.66667 13.0348 8.66667 12.6667C8.66667 12.2985 8.36819 12 8 12C7.63181 12 7.33333 12.2985 7.33333 12.6667C7.33333 13.0348 7.63181 13.3333 8 13.3333Z"
                  fill="rgba(0, 0, 0, 0.72)"
                />
              </svg>
            </div>
          </button>
        )}
      </div>

      {/* 上下文菜单 */}
      <ConversationContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        onClose={handleCloseContextMenu}
        onRename={handleStartInlineEdit}
        onDelete={handleDeleteConversation}
        isRenaming={isRenaming}
        isDeleting={isDeleting}
      />
    </>
  );
}
