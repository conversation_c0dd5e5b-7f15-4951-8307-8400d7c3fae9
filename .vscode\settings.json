{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.autoFixOnSave": true, "eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.preferences.importModuleSpecifier": "relative", "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}