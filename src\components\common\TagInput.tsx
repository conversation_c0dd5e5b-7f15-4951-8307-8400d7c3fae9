import React, { useRef, useState } from 'react';

interface TagInputProps {
  tags: string[];
  onChange: (tags: string[]) => void;
}

const TagInput: React.FC<TagInputProps> = ({ tags, onChange }) => {
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // 删除标签
  const handleRemove = (removeIdx: number) => {
    const newTags = tags.filter((_, idx) => idx !== removeIdx);
    onChange(newTags);
  };

  // 显示输入框
  const showInput = () => {
    setInputVisible(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  // 输入框回车或失焦
  const handleInputConfirm = () => {
    const value = inputValue.trim();
    if (value && !tags.includes(value)) {
      onChange([...tags, value]);
    }
    setInputValue('');
    setInputVisible(false);
  };

  return (
    <div className="flex flex-wrap gap-2 items-center">
      {tags.map((tag, idx) => (
        <span
          key={tag}
          className="h-[33px] flex items-center bg-[#FAFAFA] px-[10px] py-[6px] rounded-[6px] text-[14px] text-[#000000B8] border border-[#0000000F]"
        >
          {tag}
          <span
            className="ml-2 text-[#999] cursor-pointer text-[18px]"
            onClick={() => handleRemove(idx)}
          >
            ×
          </span>
        </span>
      ))}
      {inputVisible && (
        <input
          ref={inputRef}
          className="bg-[#FAFAFA] h-[33px] px-[10px] py-[6px]  border border-[#0000000F] rounded-[6px] text-[#000000B8] text-[14px] outline-none focus:border-[#0000001F]"
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onBlur={handleInputConfirm}
          onKeyDown={e => {
            if (e.key === 'Enter') handleInputConfirm();
          }}
          maxLength={20}
        />
      )}
      <img
        src="/assets/ai-employees/add-labal.svg"
        alt="add-tag"
        className="cursor-pointer"
        onClick={showInput}
        style={{ display: inputVisible ? 'none' : 'inline-block' }}
      />
    </div>
  );
};

export default TagInput;
