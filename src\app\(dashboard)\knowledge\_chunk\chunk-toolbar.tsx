import { KnowledgeRouteKey } from '@/constants/knowledge';
import { useGetKnowledgeSearchParams } from '@/hooks/route-hook';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  FilePdfOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import {
  App,
  Button,
  Checkbox,
  Divider,
  Flex,
  Input,
  Menu,
  MenuProps,
  Progress,
  Radio,
  RadioChangeEvent,
  Select,
  Space,
  Typography,
} from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { IChunkListResult, useSelectChunkList } from '../_hooks/chunk-hooks';
import {
  useCancelEmbedDocument,
  useEmbedDocument,
  useEmbedProgressDocument,
} from '../_hooks/document-hooks';
import VectorSvg from '../svg/vector.svg';

export enum ChunkTextMode {
  Full = 'full',
  Ellipse = 'ellipse',
}

const { Text } = Typography;

interface IProps
  extends Pick<
    IChunkListResult,
    'searchString' | 'handleInputChange' | 'available' | 'handleSetAvailable'
  > {
  checked: boolean;
  selectAllChunk: (checked: boolean) => void;
  createChunk: () => void;
  removeChunk: () => void;
  switchChunk: (available: number) => void;
  changeChunkTextMode(mode: ChunkTextMode): void;
  total: number;
  setSelectedChunkIds: string[];
  cancelBatchOperate: () => void;
  openBatchOperate: () => void;
  showBatchOperate: boolean;
}

const ChunkToolBar = ({
  selectAllChunk,
  checked,
  createChunk,
  removeChunk,
  switchChunk,
  changeChunkTextMode,
  available,
  handleSetAvailable,
  searchString,
  handleInputChange,
  total,
  setSelectedChunkIds,
  cancelBatchOperate,
  openBatchOperate,
  showBatchOperate,
}: IProps) => {
  const router = useRouter();
  const data = useSelectChunkList();
  const documentInfo = data?.documentInfo;
  const { knowledgeId: knowledgeBaseId } = useGetKnowledgeSearchParams();
  const { progress, embedProgressDocument, resetEmbedProgressDocument } =
    useEmbedProgressDocument();
  const { embedDocumentByIds } = useEmbedDocument();
  const { cancelEmbedDocument, loading: cancelVectorLoading } =
    useCancelEmbedDocument();

  const {
    modal: { confirm },
  } = App.useApp();

  const isShowVector = useMemo(() => {
    return progress !== null && progress >= 0;
  }, [progress]);

  const handleSelectAllCheck = useCallback(
    (e: any) => {
      selectAllChunk(e.target.checked);
    },
    [selectAllChunk]
  );

  const handleDelete = useCallback(() => {
    removeChunk();
  }, [removeChunk]);

  const handleEnabledClick = useCallback(() => {
    switchChunk(1);
  }, [switchChunk]);

  const handleDisabledClick = useCallback(() => {
    switchChunk(0);
  }, [switchChunk]);

  const items: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '1',
        label: (
          <>
            <Checkbox onChange={handleSelectAllCheck} checked={checked}>
              <b>{'选择所有'}</b>
            </Checkbox>
          </>
        ),
      },
      { type: 'divider' },
      {
        key: '2',
        label: (
          <Space onClick={handleEnabledClick}>
            <CheckCircleOutlined />
            <b>{'启用选定的'}</b>
          </Space>
        ),
      },
      {
        key: '3',
        label: (
          <Space onClick={handleDisabledClick}>
            <CloseCircleOutlined />
            <b>{'禁用选定的'}</b>
          </Space>
        ),
      },
      { type: 'divider' },
      {
        key: '4',
        label: (
          <Space onClick={handleDelete}>
            <DeleteOutlined />
            <b>{'删除选定的'}</b>
          </Space>
        ),
      },
    ];
  }, [
    checked,
    handleSelectAllCheck,
    handleDelete,
    handleEnabledClick,
    handleDisabledClick,
  ]);

  const content = (
    <Menu style={{ width: 200 }} items={items} selectable={false} />
  );

  const handleFilterChange = (e: RadioChangeEvent) => {
    selectAllChunk(false);
    handleSetAvailable(e.target.value);
  };

  const handleSortChange = () => {};

  const filterContent = (
    <Radio.Group onChange={handleFilterChange} value={available}>
      <Space direction="vertical">
        <Radio value={undefined}>{'所有'}</Radio>
        <Radio value={1}>{'启用'}</Radio>
        <Radio value={0}>{'禁用'}</Radio>
      </Space>
    </Radio.Group>
  );

  const filterRender = useMemo(() => {
    if (showBatchOperate) {
      return (
        <>
          <Button
            color="default"
            variant="filled"
            style={{ cursor: 'default' }}
          >
            已选择 {setSelectedChunkIds.length} 项
          </Button>
          <Button onClick={handleDelete} color="danger" variant="outlined">
            删除
          </Button>
          <Button onClick={cancelBatchOperate}>取消</Button>
        </>
      );
    } else {
      return (
        <>
          <span className="text-[#adadad]">共 {total} 个分段</span>
          {/* <Segmented
          options={[
            { label: '全文', value: ChunkTextMode.Full },
            { label: '省略', value: ChunkTextMode.Ellipse },
          ]}
          onChange={changeChunkTextMode as SegmentedProps['onChange']}
        /> */}

          <Select
            defaultValue="1"
            style={{ width: 150 }}
            onChange={handleSortChange}
            options={[{ value: '1', label: '按创建时间排序' }]}
          />
          {/* <Popover content={content} placement="bottom" arrow={false}>
          <Button>
            {'批量'}
            <DownOutlined />
          </Button>
        </Popover> */}
          {/* {isShowSearchBox ? (
          <Input
            placeholder={'搜索'}
            prefix={<SearchOutlined />}
            allowClear
            onChange={handleInputChange}
            onBlur={handleSearchBlur}
            value={searchString}
          />
        ) : (
          <Button icon={<SearchOutlined />} onClick={handleSearchIconClick} />
        )} */}
          <Button onClick={openBatchOperate}>批量操作</Button>
          {/* <Popover content={filterContent} placement="bottom" arrow={false}>
          <Button icon={<FilterIcon />} />
        </Popover> */}
          {/* <Button
          icon={<PlusOutlined />}
          type="primary"
          onClick={() => createChunk()}
        /> */}
        </>
      );
    }
  }, [total, showBatchOperate, setSelectedChunkIds]);

  // 开始向量化
  const handleStartVector = async () => {
    await embedDocumentByIds();
    embedProgressDocument();
  };
  // 取消向量化
  const handleCancelVector = async () => {
    confirm({
      icon: null,
      title: '取消向量化',
      content: '取消将使向量化进度丢失',
      cancelText: '再想想',
      okText: '确认取消',
      okButtonProps: {
        danger: true,
      },
      closable: true,
      onOk: async () => {
        await cancelEmbedDocument();
        resetEmbedProgressDocument();
      },
    });
  };

  return (
    <Flex justify="space-between" align="center">
      {/* <Space className="flex-1" size={'small'}> */}
      {/* 返回 */}

      {/* </Space> */}
      <div className="flex items-center flex-1 gap-[8px] mr-[8px]">
        <Button
          type="text"
          icon={<LeftOutlined />}
          onClick={e => {
            e.stopPropagation();
            router.push(
              `/knowledge/${KnowledgeRouteKey.Dataset}?id=${knowledgeBaseId}`
            );
          }}
        ></Button>
        {isShowVector ? (
          <div className="relative flex-1">
            <Progress
              className="flex [&_.ant-progress-inner]:rounded-[8px] [&_.ant-progress-bg]:rounded-[8px]"
              percent={progress as number}
              strokeColor="#dfeafa"
              trailColor="#ebf1f9"
              size={36}
              showInfo={false}
              status="active"
            />
            <div className="absolute top-0 left-0 w-full flex justify-between items-center h-full pl-[14px]">
              <VectorSvg className="text-[#4285f4]" />
              <div className="flex-1 mx-[8px] text-[#4285f4]">向量中...</div>
              <Button
                color="primary"
                variant="text"
                onClick={handleCancelVector}
              >
                取消
              </Button>
            </div>
          </div>
        ) : (
          <>
            {/* 文件名 */}
            <FilePdfOutlined />
            <Text
              ellipsis={{ tooltip: documentInfo?.name }}
              style={{ width: 60 }}
            >
              {documentInfo?.name}
            </Text>
            <Button
              onClick={handleStartVector}
              color="default"
              variant="solid"
              icon={<VectorSvg />}
            >
              向量化
            </Button>
            <Divider type="vertical" />
            <Input
              className="w-[200px] border-transparent shadow-none px-0"
              placeholder={'搜索'}
              prefix={
                <Image
                  src={'/assets/search/search.svg'}
                  alt=""
                  width={12}
                  height={12}
                />
              }
              allowClear
              onChange={handleInputChange}
            />
          </>
        )}
      </div>
      {!isShowVector && <Space>{filterRender}</Space>}
    </Flex>
  );
};

export default ChunkToolBar;
