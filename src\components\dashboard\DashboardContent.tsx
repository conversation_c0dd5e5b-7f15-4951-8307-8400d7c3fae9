'use client';

import React from 'react';
import Sidebar from '@/components/dashboard/Sidebar';
import Header from '@/components/dashboard/Header';
import MainContent from '@/components/dashboard/MainContent';
import { useAuth } from '@/lib/auth/AuthProvider';
import { usePageConfigInit } from '@/hooks/usePageConfig';

interface DashboardContentProps {
  children: React.ReactNode;
  collapsed: boolean;
  onToggle: () => void;
}

export default function DashboardContent({
  children,
  collapsed,
  onToggle,
}: DashboardContentProps) {
  const { isInitialized, isAuthenticated } = useAuth();

  // 初始化页面配置（包括默认面包屑）
  usePageConfigInit();
  return (
    <>
      {/* Dashboard内容 */}
      <div className="flex h-screen bg-bg-base">
        {/* 侧边栏 */}
        <Sidebar collapsed={collapsed} onToggle={onToggle} />

        {/* 主内容区域 */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* 顶部导航栏 */}
          <Header onToggle={onToggle} />

          {/* 主内容 */}
          <MainContent>
            {!isInitialized ? (
              // 认证初始化中，显示加载状态
              <div className="flex items-center justify-center h-screen bg-bg-base">
                <div className="text-text-secondary">正在初始化认证...</div>
              </div>
            ) : !isAuthenticated ? (
              <div className="flex items-center justify-center h-screen bg-bg-base">
                <div className="text-text-secondary">认证失败，请重新登录</div>
              </div>
            ) : (
              children
            )}
          </MainContent>
        </div>
      </div>
    </>
  );
}
