import { Authorization } from '@/constants/authorization';
import { getAuthorization } from '@/utils/authorization-util';
import jsPreviewExcel from '@js-preview/excel';
import '@js-preview/excel/lib/index.css';
import axios from 'axios';
import mammoth from 'mammoth';
import { useCallback, useEffect, useRef, useState } from 'react';

export const useCatchError = (api: string) => {
  const [error, setError] = useState('');
  const fetchDocument = useCallback(async () => {
    const ret = await axios.get(api);
    const { data } = ret;
    if (!(data instanceof ArrayBuffer) && data.code !== 0) {
      setError(data.message);
    }
    return ret;
  }, [api]);

  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return { fetchDocument, error };
};

export const useFetchDocument = () => {
  const fetchDocument = useCallback(async (api: string) => {
    const ret = await axios.get(api, {
      headers: {
        [Authorization]: getAuthorization(),
      },
      responseType: 'arraybuffer',
    });
    return ret;
  }, []);

  return { fetchDocument };
};

export const useFetchExcel = (filePath: string) => {
  const [status, setStatus] = useState(true);
  const { fetchDocument } = useFetchDocument();
  const containerRef = useRef<HTMLDivElement>(null);
  const { error } = useCatchError(filePath);
  const excelPreviewerRef = useRef<any>(null);

  const fetchDocumentAsync = useCallback(async () => {
    try {
      // 清理之前的Excel预览器实例
      if (excelPreviewerRef.current) {
        excelPreviewerRef.current.destroy();
        excelPreviewerRef.current = null;
      }

      // 清空容器内容
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
      }

      // 初始化新的Excel预览器
      if (containerRef.current) {
        excelPreviewerRef.current = jsPreviewExcel.init(containerRef.current);
      }

      const jsonFile = await fetchDocument(filePath);

      if (excelPreviewerRef.current) {
        excelPreviewerRef.current
          .preview(jsonFile.data)
          .then(() => {
            console.log('Excel预览成功');
            setStatus(true);
          })
          .catch((e: any) => {
            console.warn('Excel预览失败', e);
            if (excelPreviewerRef.current) {
              excelPreviewerRef.current.destroy();
              excelPreviewerRef.current = null;
            }
            setStatus(false);
          });
      }
    } catch (error) {
      console.error('Excel预览错误:', error);
      if (excelPreviewerRef.current) {
        excelPreviewerRef.current.destroy();
        excelPreviewerRef.current = null;
      }
      setStatus(false);
    }
  }, [filePath, fetchDocument]);

  useEffect(() => {
    fetchDocumentAsync();
  }, [fetchDocumentAsync]);

  // 组件卸载时清理Excel预览器
  useEffect(() => {
    return () => {
      if (excelPreviewerRef.current) {
        excelPreviewerRef.current.destroy();
        excelPreviewerRef.current = null;
      }
    };
  }, []);

  return { status, containerRef, error };
};

export const useFetchDocx = (filePath: string) => {
  const [succeed, setSucceed] = useState(true);
  const [error, setError] = useState<string>();
  const { fetchDocument } = useFetchDocument();
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchDocumentAsync = useCallback(async () => {
    try {
      const jsonFile = await fetchDocument(filePath);
      mammoth
        .convertToHtml(
          { arrayBuffer: jsonFile.data },
          { includeDefaultStyleMap: true }
        )
        .then(result => {
          setSucceed(true);
          const docEl = document.createElement('div');
          docEl.className = 'document-container';
          docEl.innerHTML = result.value;
          const container = containerRef.current;
          if (container) {
            container.innerHTML = docEl.outerHTML;
          }
        })
        .catch(() => {
          setSucceed(false);
        });
    } catch (error: any) {
      setError(error.toString());
    }
  }, [filePath, fetchDocument]);

  useEffect(() => {
    fetchDocumentAsync();
  }, [fetchDocumentAsync]);

  return { succeed, containerRef, error };
};

export const useFetchText = (filePath: string) => {
  const [succeed, setSucceed] = useState(true);
  const [error, setError] = useState<string>();
  const { fetchDocument } = useFetchDocument();
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchDocumentAsync = useCallback(async () => {
    try {
      const jsonFile = await fetchDocument(filePath);
      const container = containerRef.current;
      if (container) {
        const textDecoder = new TextDecoder('utf-8');
        const textContent = textDecoder.decode(jsonFile.data);

        const preElement = document.createElement('pre');
        preElement.textContent = textContent;

        container.innerHTML = '';
        container.appendChild(preElement);
        setSucceed(true);
      }
    } catch (error: any) {
      setError(error.toString());
      setSucceed(false);
    }
  }, [filePath, fetchDocument]);

  useEffect(() => {
    fetchDocumentAsync();
  }, [fetchDocumentAsync]);

  return { succeed, containerRef, error };
};
