'use client';

import { ChatMessage } from '@/lib/chat-engine/types';
import {
  HttpTransportType,
  HubConnection,
  HubConnectionBuilder,
  LogLevel,
} from '@microsoft/signalr';
import { AGUIEvent, EventType } from '../agui/types';

export interface WebSocketConfig {
  baseUrl?: string;
  enableLogging?: boolean;
}

export interface WebSocketEventHandlers {
  onMessage: (message: ChatMessage) => void;
  onStreamingStart: (messageId: string) => void;
  onStreamingContent: (messageId: string, delta: string) => void;
  onStreamingEnd: (messageId: string) => void;
  onToolCallStart: (toolCallId: string, toolName: string) => void;
  onToolCallArgs: (toolCallId: string, delta: string) => void;
  onToolCallEnd: (toolCallId: string, finalArgs: string) => void;
  onToolCallResult: (toolCallId: string, result: string) => void;
  onRunStarted: (runId: string) => void;
  onRunFinished: (runId: string) => void;
  onError: (error: string) => void;
  onConnectionChange: (connected: boolean) => void;
}

/**
 * WebSocketManager - 简化的WebSocket连接管理
 * 专注于WebSocket连接和消息事件处理，不涉及业务逻辑
 */
export class WebSocketManager {
  private connection: HubConnection | null = null;
  private config: WebSocketConfig;
  private currentConversationId: string | null = null;
  private handlers: Partial<WebSocketEventHandlers> = {};
  private isConnecting = false;

  // 工具调用状态追踪
  private toolCallArgsMap = new Map<string, string[]>();

  constructor(config: WebSocketConfig = {}) {
    this.config = {
      baseUrl: config.baseUrl || '/ws/chatHub',
      enableLogging:
        config.enableLogging ?? process.env.NODE_ENV === 'development',
    };
  }

  /**
   * 连接到指定会话
   */
  async connect(conversationId: string, token?: string): Promise<void> {
    if (this.currentConversationId === conversationId && this.isConnected()) {
      this.log(`已连接到会话: ${conversationId}`);
      return;
    }

    // 如果正在连接，等待完成
    if (this.isConnecting) {
      await this.waitForConnection();
      return;
    }

    this.isConnecting = true;

    try {
      // 断开现有连接
      if (this.connection) {
        await this.disconnect();
      }

      // 构建连接URL
      const hubUrl = `${this.config.baseUrl}?conversationId=${conversationId}${
        token ? `&access_token=${token}` : ''
      }`;

      this.log(
        `开始连接: ${hubUrl.replace(/access_token=[^&]*/, 'access_token=***')}`
      );

      // 创建新连接
      this.connection = new HubConnectionBuilder()
        .withUrl(hubUrl, {
          skipNegotiation: true,
          transport: HttpTransportType.WebSockets,
        })
        .withAutomaticReconnect()
        .configureLogging(
          this.config.enableLogging ? LogLevel.Information : LogLevel.Error
        )
        .build();

      // 设置事件监听
      this.setupEventListeners();

      // 启动连接
      await this.connection.start();

      this.currentConversationId = conversationId;
      this.log(`连接成功: ${conversationId}`);

      // 通知连接状态变化
      this.handlers.onConnectionChange?.(true);
    } catch (error) {
      this.log(`连接失败: ${conversationId}`, error);
      this.handlers.onError?.(`连接失败: ${error}`);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (!this.connection) {
      return;
    }

    this.log(`断开连接: ${this.currentConversationId}`);

    try {
      await this.connection.stop();
    } catch (error) {
      this.log('断开连接时发生错误:', error);
    } finally {
      this.connection = null;
      this.currentConversationId = null;

      // 通知连接状态变化
      this.handlers.onConnectionChange?.(false);
    }
  }

  /**
   * 发送消息（预留接口，实际发送仍通过HTTP API）
   */
  async sendMessage(content: string): Promise<void> {
    if (!this.connection || !this.isConnected()) {
      throw new Error('WebSocket未连接');
    }

    // 这里可以根据需要实现WebSocket发送消息
    // 目前保持HTTP API发送，WebSocket只负责接收
    this.log('发送消息（通过HTTP API）:', content.slice(0, 50));
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connection?.state === 'Connected';
  }

  /**
   * 设置事件处理器
   */
  setEventHandlers(handlers: Partial<WebSocketEventHandlers>): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.connection) return;

    // 监听客户端消息响应
    this.connection.on('OnMessageReceivedFromClient', (message: any) => {
      this.log('收到完整消息:', message);

      const chatMessage: ChatMessage = {
        id: 'user_' + message.message_id,
        conversationId: this.currentConversationId!,
        role: message.sender?.role || 'user',
        content: message.text || '',
        timestamp: new Date(message.created_at).getTime(),
        status: 'sent',
      };

      this.handlers.onMessage?.(chatMessage);
    });

    // 监听AGUI事件 - 这是核心的事件处理
    this.connection.on('AGUIEvent', (event: AGUIEvent) => {
      this.handleAGUIEvent(event);
    });

    // 监听连接状态变化
    this.connection.onreconnecting(() => {
      this.log('正在重连...');
      this.handlers.onConnectionChange?.(false);
    });

    this.connection.onreconnected(() => {
      this.log('重连成功');
      this.handlers.onConnectionChange?.(true);
    });

    this.connection.onclose(error => {
      this.log('连接关闭:', error);
      this.handlers.onConnectionChange?.(false);

      if (error) {
        this.handlers.onError?.(`连接关闭: ${error}`);
      }
    });
  }

  /**
   * 处理AGUI事件 - 基于convertChatEvent的逻辑
   */
  private handleAGUIEvent(event: AGUIEvent): void {
    if (this.config.enableLogging) {
      this.log('收到AGUI事件:', event);
    }

    try {
      switch (event.type) {
        case EventType.RUN_STARTED:
          this.handlers.onRunStarted?.(event.runId || '');
          break;

        case EventType.TEXT_MESSAGE_START:
          this.handlers.onStreamingStart?.(event.messageId || '');
          break;

        case EventType.TEXT_MESSAGE_CONTENT:
          this.handlers.onStreamingContent?.(
            event.messageId || '',
            event.delta || ''
          );
          break;

        case EventType.TEXT_MESSAGE_END:
          this.handlers.onStreamingEnd?.(event.messageId || '');
          break;

        case EventType.TOOL_CALL_START:
          this.handlers.onToolCallStart?.(
            event.toolCallId || '',
            event.toolCallName || ''
          );
          // 初始化工具调用参数累积
          this.toolCallArgsMap.set(event.toolCallId || '', []);
          break;

        case EventType.TOOL_CALL_ARGS:
          // 累积工具调用参数
          const toolCallId = event.toolCallId || '';
          const delta = event.delta || '';

          if (this.toolCallArgsMap.has(toolCallId)) {
            this.toolCallArgsMap.get(toolCallId)!.push(delta);
          }

          this.handlers.onToolCallArgs?.(toolCallId, delta);
          break;

        case EventType.TOOL_CALL_END:
          // 获取完整的工具调用参数
          const endToolCallId = event.toolCallId || '';
          const argsParts = this.toolCallArgsMap.get(endToolCallId) || [];
          const finalArgs = argsParts.join('');

          this.handlers.onToolCallEnd?.(endToolCallId, finalArgs);

          // 清理参数累积
          this.toolCallArgsMap.delete(endToolCallId);
          break;

        case EventType.TOOL_CALL_RESULT:
          this.handlers.onToolCallResult?.(
            event.toolCallId || '',
            event.content || ''
          );
          break;

        case EventType.RUN_FINISHED:
          this.handlers.onRunFinished?.(event.runId || '');
          break;

        case EventType.RUN_ERROR:
          this.handlers.onError?.(
            event.message || event.error || 'AGUI运行错误'
          );
          break;

        default:
          if (this.config.enableLogging) {
            this.log(`未处理的AGUI事件类型: ${event.type}`, event);
          }
          break;
      }
    } catch (error) {
      this.log('处理AGUI事件时出错:', error);
      this.handlers.onError?.(`事件处理错误: ${error}`);
    }
  }

  /**
   * 等待连接完成
   */
  private async waitForConnection(): Promise<void> {
    const maxWaitTime = 5000; // 5秒超时
    const checkInterval = 100; // 100ms检查一次
    let waitTime = 0;

    while (this.isConnecting && waitTime < maxWaitTime) {
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waitTime += checkInterval;
    }

    if (this.isConnecting) {
      throw new Error('连接超时');
    }
  }

  /**
   * 日志输出
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      console.log(`[WebSocketManager] ${message}`, data || '');
    }
  }

  /**
   * 获取当前会话ID
   */
  getCurrentConversationId(): string | null {
    return this.currentConversationId;
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    await this.disconnect();
    this.handlers = {};
    this.toolCallArgsMap.clear();
    this.log('WebSocketManager destroyed');
  }
}

// 导出单例实例
export const webSocketManager = new WebSocketManager({
  enableLogging: process.env.NODE_ENV === 'development',
});
