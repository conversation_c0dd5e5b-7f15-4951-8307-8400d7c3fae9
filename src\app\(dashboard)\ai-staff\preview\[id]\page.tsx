'use client';
import { getEmployeeDetail } from '@/api/employee';
import EmployeePageLayout from '@/components/ai-staff/EmployeePageLayout';
import { useBreadcrumb } from '@/hooks/usePageConfig';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

export default function PreviewEmployeePage() {
  const router = useRouter();
  const { id } = useParams();
  const [loading, setLoading] = useState(true);
  const [employee, setEmployee] = useState<any>(null);
  const { setTitle } = useBreadcrumb();

  useEffect(() => {
    async function fetchDetail() {
      setLoading(true);

      try {
        const detail = await getEmployeeDetail(id as string);
        setEmployee(detail);

        // 设置动态面包屑标题
        setTitle(`预览员工 - ${detail.name}`, false);
      } catch {
        toast.error('获取员工详情失败');
        // 如果获取员工信息失败，显示默认标题
        setTitle('预览员工', false);
      } finally {
        setLoading(false);
      }
    }
    if (id) fetchDetail();
  }, [id]);

  if (loading) return <div className="p-8 text-center">加载中...</div>;
  if (!employee)
    return <div className="p-8 text-center text-red-500">未找到员工</div>;

  return (
    <EmployeePageLayout
      mode="preview"
      initialValues={employee}
      loading={loading}
      onBack={() => router.push('/ai-staff')}
      agentId={employee.agent?.agent_id}
    />
  );
}
