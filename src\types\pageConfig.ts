import { ReactNode } from 'react';

/**
 * 面包屑配置
 */
export interface BreadcrumbConfig {
  /** 面包屑标题 */
  title: string;
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 自定义面包屑项 */
  items?: BreadcrumbItem[];
}

/**
 * 面包屑项
 */
export interface BreadcrumbItem {
  /** 显示文本 */
  title: string;
  /** 链接地址（可选） */
  href?: string;
  /** 是否为当前页 */
  current?: boolean;
}

/**
 * 页面操作按钮配置
 */
export interface PageAction {
  /** 按钮唯一标识 */
  key: string;
  /** 按钮内容 */
  content: ReactNode;
  /** 按钮位置 */
  position?: 'left' | 'right';
}

/**
 * 页面配置
 */
export interface PageConfig {
  /** 面包屑配置 */
  breadcrumb?: BreadcrumbConfig;
  /** 页面标题 */
  title?: string;
  /** 页面操作按钮 */
  actions?: PageAction[];
  /** 页面元数据（可扩展） */
  meta?: Record<string, any>;
}

/**
 * 页面配置更新函数类型
 */
export type PageConfigSetter = (config: Partial<PageConfig>) => void;

/**
 * 面包屑设置函数类型
 */
export type BreadcrumbSetter = (config: BreadcrumbConfig) => void;
