'use client';

import { ConversationItem as ConversationItemType } from '@/types/conversation';
import { useCallback, useEffect, useRef } from 'react';
import ConversationItem from './ConversationItem';
import { useHistoryContext } from './HistoryProvider';
import { useConversations } from './hooks/useConversations';
import { useRouteSync } from './hooks/useRouteSync';

export default function ConversationList() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const { userId } = useHistoryContext();

  const {
    groupedConversations,
    selectedConversationId,
    loading,
    error,
    hasMore,
    isDeletingConversation,
    isRenamingConversation,
    loadConversations,
    loadMore,
    deleteConversation,
    renameConversation,
    clearError,
  } = useConversations();

  const { navigateToConversation } = useRouteSync(userId);

  // 初始加载会话列表
  useEffect(() => {
    if (userId && groupedConversations.length === 0 && !loading) {
      console.log('🔄 [ConversationList] 初始加载会话列表');
      loadConversations(userId);
    }
  }, [userId, groupedConversations.length, loading, loadConversations]);

  // 滚动到底部加载更多
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container || loading) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;

    if (isNearBottom && hasMore) {
      loadMore(userId);
    }
  }, [loading, hasMore, loadMore, userId]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // 会话点击处理
  const handleConversationClick = useCallback(
    (conversationId: string) => {
      navigateToConversation(conversationId);
    },
    [navigateToConversation]
  );

  // 删除会话处理
  const handleDeleteConversation = useCallback(
    async (conversation: ConversationItemType) => {
      const success = await deleteConversation(
        conversation.agent_id,
        conversation.id
      );
      if (success && selectedConversationId === conversation.id) {
        // 如果删除的是当前选中的会话，自动选择下一个会话
        const allConversations = groupedConversations.flatMap(
          group => group.conversations
        );
        const remainingConversations = allConversations.filter(
          c => c.id !== conversation.id
        );
        if (remainingConversations.length > 0) {
          navigateToConversation(remainingConversations[0].id);
        }
      }
    },
    [
      deleteConversation,
      selectedConversationId,
      groupedConversations,
      navigateToConversation,
    ]
  );

  // 重命名会话处理
  const handleRenameConversation = useCallback(
    async (conversation: ConversationItemType, newTitle: string) => {
      await renameConversation(
        conversation.agent_id,
        conversation.id,
        newTitle
      );
    },
    [renameConversation]
  );

  // 会话列表加载错误处理
  if (error) {
    return (
      <div className="flex flex-col h-full w-[280px] bg-layer-1 border-r border-border-light">
        <div className="p-4 border-b border-border-light">
          <div className="text-error text-sm">加载会话列表失败</div>
          <button
            onClick={() => {
              clearError();
              loadConversations(userId, true);
            }}
            className="mt-2 text-primary text-sm hover:underline"
          >
            点击重试
          </button>
        </div>
      </div>
    );
  }

  // 初始加载状态
  if (groupedConversations.length === 0 && loading) {
    return (
      <div className="flex flex-col w-[280px] h-full bg-layer-1 border-r border-border-light p-2">
        <div className="p-8 text-center space-y-3 w-full">
          <div className="text-text-muted text-sm">加载会话列表中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-[280px] h-full bg-layer-1 border-r border-border-light p-2">
      {/* 滚动容器 */}
      <div className="relative flex-1 overflow-hidden">
        {/* 渐变背景 */}
        <div className="absolute inset-0 pointer-events-none z-10" />

        {/* 滚动内容 */}
        <div
          ref={scrollContainerRef}
          className="absolute inset-0 overflow-y-auto scrollbar-none"
        >
          {/* 会话列表 */}
          <div className="flex flex-col items-start pt-4 pb-20 gap-1">
            {/* 空状态 */}
            {!loading && groupedConversations.length === 0 && (
              <div className="p-8 text-center space-y-3 w-full">
                <svg
                  className="w-12 h-12 mx-auto text-text-muted"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                <div className="text-text-muted text-sm">暂无会话记录</div>
              </div>
            )}

            {/* 分组会话列表 */}
            {groupedConversations.map((group, groupIndex) => (
              <div key={groupIndex} className="w-full">
                {/* 时间分组标题 */}
                <div className="flex flex-row justify-between items-center px-2.5 py-1.5 gap-3 w-66 h-7.5">
                  <span className="flex-1 text-xs font-medium leading-4.5 text-text-muted">
                    {group.label}
                  </span>
                </div>

                {/* 该分组的会话项 */}
                {group.conversations.map(conversation => (
                  <ConversationItem
                    key={conversation.id}
                    conversation={conversation}
                    isSelected={selectedConversationId === conversation.id}
                    isDeleting={isDeletingConversation === conversation.id}
                    isRenaming={isRenamingConversation === conversation.id}
                    onClick={() => handleConversationClick(conversation.id)}
                    onDelete={() => handleDeleteConversation(conversation)}
                    onRename={newTitle =>
                      handleRenameConversation(conversation, newTitle)
                    }
                  />
                ))}
              </div>
            ))}

            {/* 加载更多指示器 */}
            {loading && groupedConversations.length > 0 && (
              <div className="p-4 text-center w-full">
                <div className="text-text-muted text-sm">加载更多...</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
