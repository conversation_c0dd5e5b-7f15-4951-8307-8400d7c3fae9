'use client';

import React, { useEffect, useRef } from 'react';

interface EmployeeContextMenuProps {
  isVisible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onToggleStatus: () => void;
  isEditing?: boolean;
  isDeleting?: boolean;
  isDisabled: boolean;
}

export default function EmployeeContextMenu({
  isVisible,
  position,
  onClose,
  onEdit,
  onDelete,
  onToggleStatus,
  isEditing = false,
  isDeleting = false,
  isDisabled,
}: EmployeeContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-100%, 0%)',
      }}
    >
      <div className="flex flex-col items-start p-1.5 mt-1.5 gap-1 w-[160px] bg-white border border-[#0000000F] shadow-lg rounded-lg">
        {/* 编辑按钮 */}
        <button
          onClick={onEdit}
          disabled={isEditing || isDeleting}
          className="cursor-pointer flex flex-row items-center px-2 py-1.5 gap-3 w-full h-[33px] rounded-md hover:bg-[#0000000A] transition-colors disabled:opacity-50"
        >
          <img src="/assets/ai-employees/edit.svg" alt="edit" />

          <span className="text-[14px] font-normal leading-[100%] text-[#000000B8]">
            {isEditing ? '编辑中...' : '编辑'}
          </span>
        </button>

        {/* 删除按钮 */}
        <button
          onClick={onDelete}
          disabled={isEditing || isDeleting}
          className="cursor-pointer flex flex-row items-center px-2 py-1.5 gap-3 w-full h-[33px] rounded-md hover:bg-[#0000000A] transition-colors disabled:opacity-50"
        >
          <img src="/assets/ai-employees/delete.svg" alt="edit" />

          <span className="text-[14px] font-normal leading-[100%] text-[#000000B8]">
            {isDeleting ? '删除中...' : '删除'}
          </span>
        </button>

        <div className="w-full h-[1px] bg-[#0000000F]"></div>

        {/* 启用/禁用按钮 */}
        <button
          onClick={onToggleStatus}
          disabled={isEditing || isDeleting}
          className="cursor-pointer flex flex-row items-center px-2 py-1.5 gap-3 w-full h-[33px] rounded-md hover:bg-[#0000000A] transition-colors disabled:opacity-50"
        >
          <img src="/assets/ai-employees/enable.svg" alt="enable" />

          <span className="text-[14px] font-normal leading-[100%] text-[#000000B8]">
            {isDisabled ? '启用' : '禁用'}
          </span>
        </button>
      </div>
    </div>
  );
}
