import Docx from '@/components/file-manager/document-viewer/docx';
import Excel from '@/components/file-manager/document-viewer/excel';
import Image from '@/components/file-manager/document-viewer/image';
import Pdf from '@/components/file-manager/document-viewer/pdf';
import Text from '@/components/file-manager/document-viewer/text';
import { Images } from '@/constants/common';
import { api_host } from '@/utils/api';
import { previewHtmlFile } from '@/utils/file-util';
import { Flex, Modal } from 'antd';
import { useEffect, useState } from 'react';

// 添加样式
const styles = {
  modalBody: {
    padding: 0,
    overflow: 'hidden',
  },
  viewerWrapper: {
    height: '80vh',
    padding: 0,
    width: '100%',
    overflow: 'auto',
  },
  imageContainer: {
    height: '100%',
    width: '100%',
  },
  imageStyle: {
    maxWidth: '100%',
    maxHeight: '100%',
    objectFit: 'contain' as const,
  },
};

interface IDocumentPreviewModalProps {
  visible: boolean;
  onClose: () => void;
  documentId?: string;
  documentName?: string;
  prefix?: string;
}

const DocumentPreviewModal = ({
  visible,
  onClose,
  documentId,
  documentName,
  prefix = 'file',
}: IDocumentPreviewModalProps) => {
  const [ext, setExt] = useState<string>('');
  const api = `${api_host}/${prefix}/get/${documentId}`;

  useEffect(() => {
    if (documentName) {
      const extension = documentName
        .slice(documentName.lastIndexOf('.') + 1)
        .toLowerCase();
      setExt(extension);
    }
  }, [documentName]);

  // 为图片添加样式
  useEffect(() => {
    if (Images.includes(ext)) {
      const imageElements = document.querySelectorAll('.image img');
      imageElements.forEach(img => {
        if (img instanceof HTMLElement) {
          Object.assign(img.style, styles.imageStyle);
        }
      });
    }
  }, [ext]);

  const handleClose = () => {
    onClose();
  };

  // HTML 文件特殊处理
  if (ext === 'html' && documentId) {
    previewHtmlFile(documentId);
    handleClose();
    return null;
  }

  return (
    <Modal
      title={documentName || '文档预览'}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width="90%"
      style={{ top: 20 }}
      className="previewModal"
      styles={{
        body: styles.modalBody,
      }}
    >
      <div className="viewerWrapper" style={styles.viewerWrapper}>
        {Images.includes(ext) && (
          <Flex
            className="image"
            align="center"
            justify="center"
            style={styles.imageContainer}
          >
            <Image src={api} preview={false} />
          </Flex>
        )}
        {ext === 'pdf' && <Pdf url={api} />}
        {(ext === 'xlsx' || ext === 'xls') && <Excel filePath={api} />}
        {ext === 'docx' && <Docx filePath={api} />}
        {['txt', 'json', 'yaml', 'yml'].includes(ext) && (
          <Text filePath={api} />
        )}
      </div>
    </Modal>
  );
};

export default DocumentPreviewModal;
