import { Images } from '@/constants/common';
import { api_host } from '@/utils/api';
import { Flex } from 'antd';
import { useParams, useSearchParams } from 'next/navigation';
import Docx from './docx';
import Excel from './excel';
import Image from './image';
import Pdf from './pdf';
import Text from './text';

import { previewHtmlFile } from '@/utils/file-util';
// import styles from './index.module.less';

// TODO: The interface returns an incorrect content-type for the SVG.

const DocumentViewer = () => {
  const { id: documentId } = useParams();
  const searchParams = useSearchParams();
  const ext = searchParams.get('ext');
  const prefix = searchParams.get('prefix');
  const api = `${api_host}/${prefix || 'file'}/get/${documentId}`;

  if (ext === 'html' && documentId && typeof documentId === 'string') {
    previewHtmlFile(documentId);
    return;
  }

  return (
    <section
      className="viewerWrapper"
      style={{ width: '100%', height: '100%' }}
    >
      {Images.includes(ext!) && (
        <Flex
          className="image"
          align="center"
          justify="center"
          style={{ width: '100%', height: '100%' }}
        >
          <Image src={api} preview={false}></Image>
        </Flex>
      )}
      {ext === 'pdf' && <Pdf url={api}></Pdf>}
      {(ext === 'xlsx' || ext === 'xls') && <Excel filePath={api}></Excel>}

      {ext === 'docx' && <Docx filePath={api}></Docx>}
      {['txt', 'json', 'yaml', 'yml'].includes(ext!) && (
        <Text filePath={api}></Text>
      )}
    </section>
  );
};

export default DocumentViewer;
