'use client';

import React from 'react';
import { usePageConfigContext } from '@/contexts/PageConfigContext';

interface HeaderProps {
  onToggle: () => void;
}

export default function Header({ onToggle }: HeaderProps) {
  const { config } = usePageConfigContext();

  return (
    <header className="flex items-center justify-between h-[56px] px-6 bg-[#FAFAFA] border-b border-border-light">
      {/* 左侧：移动端菜单按钮 + 面包屑 */}
      <div className="flex items-center space-x-2">
        {/* 菜单切换按钮 */}
        <button
          onClick={onToggle}
          className="hover:bg-layer-2 rounded-lg transition-colors cursor-pointer"
        >
          <img src="/sidebar-control.svg" alt="" />
        </button>

        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2">
          <span className="text-text-muted text-sm">首页</span>
          <span className="text-text-muted text-sm">/</span>
          <div className="flex items-center space-x-1">
            <span className="text-text-primary text-sm font-medium">
              {config.breadcrumb?.title || '加载中...'}
            </span>
            {config.breadcrumb?.loading && (
              <div className="animate-spin w-3 h-3 border border-gray-300 border-t-blue-600 rounded-full"></div>
            )}
          </div>
        </nav>
      </div>
    </header>
  );
}
