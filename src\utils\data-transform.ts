/**
 * 数据转换工具
 * 用于将 API 响应数据转换为前端期望的格式
 */

import { ConversationMessage } from '@/types/conversation';

/**
 * API 原始消息项接口
 */
interface ApiMessageItem {
  conversation_id: string;
  sender: {
    id: string;
    user_name: string;
    first_name: string;
    last_name?: string;
    role: string;
    full_name: string;
    [key: string]: any;
  };
  function: any;
  has_message_files: boolean;
  created_at: string;
  message_id: string;
  text: string;
  data: any;
  states: any;
  rich_content: any;
}

/**
 * 转换 API 消息项为前端格式
 */
export function transformApiMessageItem(
  apiMessage: ApiMessageItem
): ConversationMessage {
  return {
    message_id: apiMessage.message_id,
    conversation_id: apiMessage.conversation_id,
    text: apiMessage.text,
    role: mapApiRole(apiMessage.sender.role),
    created_at: apiMessage.created_at,
    sender: {
      id: apiMessage.sender.id,
      name: apiMessage.sender.full_name || apiMessage.sender.first_name,
      role: apiMessage.sender.role,
    },
    isStreaming: false,
    states: apiMessage.states,
    rich_content: apiMessage.rich_content,
  };
}

/**
 * 转换 API 消息列表为前端格式
 */
export function transformApiMessageList(
  apiMessages: ApiMessageItem[]
): ConversationMessage[] {
  return apiMessages.map(transformApiMessageItem);
}

/**
 * 映射 API 角色到前端角色
 */
function mapApiRole(apiRole: string): 'user' | 'assistant' | 'system' {
  switch (apiRole.toLowerCase()) {
    case 'assistant':
    case 'bot':
    case 'agent':
      return 'assistant';
    case 'user':
    case 'client':
      return 'user';
    case 'system':
      return 'system';
    default:
      return 'assistant';
  }
}

/**
 * 将 ISO 8601 字符串格式化为 'YYYY/MM/DD HH:mm'（本地时间）
 * @param isoString ISO 8601 字符串
 * @returns 格式化后的字符串
 */
export function formatDateTime(isoString?: string): string {
  if (!isoString) return '';
  const date = new Date(isoString);
  if (isNaN(date.getTime())) return '';
  const pad = (n: number) => n.toString().padStart(2, '0');
  return `${date.getFullYear()}/${pad(date.getMonth() + 1)}/${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
}
