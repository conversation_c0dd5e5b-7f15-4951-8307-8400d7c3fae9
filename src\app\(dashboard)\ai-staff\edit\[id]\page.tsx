'use client';
import { getEmployeeDetail, updateEmployee } from '@/api/employee';
import EmployeePageLayout, {
  EmployeeFormValues,
} from '@/components/ai-staff/EmployeePageLayout';
import { useBreadcrumb } from '@/hooks/usePageConfig';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function EditEmployeePage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [loading, setLoading] = useState(false);
  const [initialValues, setInitialValues] = useState<
    Partial<EmployeeFormValues>
  >({});
  const [agentId, setAgentId] = useState<string | undefined>(undefined);
  const { setTitle } = useBreadcrumb();

  useEffect(() => {
    if (!id) return;

    setLoading(true);

    getEmployeeDetail(id)
      .then(data => {
        setInitialValues({
          name: data.name,
          description: data.description,
          tags: data.tags,
          isPublic: data.isPublic,
          prompt: data.agent.instruction,
          knowledgeIds: data.agent.knowledge_bases || [],
          agentId: data.agentId,
          agentDescription: data.agent.description,
        });

        setAgentId(data.agentId);

        // 设置动态面包屑标题
        setTitle(`编辑员工 - ${data.name}`, false);
      })
      .catch(() => {
        // 如果获取员工信息失败，显示默认标题
        setTitle('编辑员工', false);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [id]);

  // TODO: 替换为updateEmployee API
  const handleUpdate = async (values: EmployeeFormValues) => {
    await updateEmployee(id, values);
  };

  return (
    <EmployeePageLayout
      mode="edit"
      initialValues={initialValues}
      onSubmit={handleUpdate}
      submitText="保存"
      loading={loading}
      onBack={() => router.push('/ai-staff')}
      employeeId={id}
      agentId={agentId}
    />
  );
}
