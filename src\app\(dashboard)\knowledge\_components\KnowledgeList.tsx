import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Divider, Layout, Select, Skeleton, Spin, Tag } from 'antd';
import { useCallback, useMemo, useRef, useState } from 'react';
// import IconScreen from "@/assets/application/screen.svg";
import { formatDate } from '@/utils/date';
import { useRouter } from 'next/navigation';
import {
  useFetchGetKbTags,
  useInfiniteFetchKnowledgeList,
} from '../_hooks/knowledge-hooks';
import KnowledgeCard, { Action } from './KnowledgeCard';
// import IconEmptyModelConfig from "@/assets/svg/IconEmptyModelConfig.svg";
import InfiniteScroll from 'react-infinite-scroll-component';
import { useSetNextDocumentStatus } from '../_hooks/document-hooks';
import DeleteKnowledgeModal from './DeleteKnowledgeModal';
import EmptyData from './EmptyData';
import KnowledgeModal from './KnowledgeModal';

const Option = Select.Option;
const { Content } = Layout;

const initActionButtons: Action[] = [
  {
    label: '编辑',
    className: 'text-[#333333]',
    type: 'edit',
    icon: <EditOutlined />,
  },
  {
    label: '删除',
    className: 'text-[#333333]',
    type: 'del',
    icon: <DeleteOutlined />,
  },
] as const;

// 知识库列表
export default function KnowledgeList() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'personal' | 'shared'>('personal');

  const {
    fetchNextPage,
    data,
    hasNextPage,
    searchString,
    handleInputChange,
    loading,
    refetch,
    tags,
    setTags,
  } = useInfiniteFetchKnowledgeList(activeTab);

  const { kbTags } = useFetchGetKbTags();

  const { setDocumentStatus } = useSetNextDocumentStatus();

  const nextList = useMemo(() => {
    const list =
      data?.pages?.flatMap(x => (Array.isArray(x.kbs) ? x.kbs : [])) ?? [];
    return list;
  }, [data?.pages]);

  const total = useMemo(() => {
    return data?.pages.at(-1).total ?? 0;
  }, [data?.pages]);

  const knowledgeModalRef = useRef<{
    open: (form?: any) => void;
  }>(null);

  const deleteKnowledgeModalRef = useRef<{
    open: (knowledge: any) => void;
    close: () => void;
  }>(null);

  const handleAction = async (row: any, type: Action['type']) => {
    switch (type) {
      case 'enable':
        break;
      // 禁用
      case 'disable':
        break;
      case 'edit':
        knowledgeModalRef.current?.open(row);
        break;
      case 'del':
        confirmDelete(row);
        break;
    }
  };

  const confirmDelete = (data: any) => {
    deleteKnowledgeModalRef.current?.open(data);
  };

  const getActionButtons = useCallback(
    (row: any) => {
      if (row.status === '1') {
        const disableActionButtons: Action[] = [
          {
            label: '禁用',
            className: 'text-[#d54941]',
            type: 'disable',
            icon: <></>,
          },
        ];
        return initActionButtons.concat(disableActionButtons);
      }
      const startActionButtons: Action[] = [
        {
          label: '启动',
          className: 'text-[#2ba471]',
          type: 'enable',
          icon: <></>,
        },
      ];
      return initActionButtons.concat(startActionButtons);
    },
    [initActionButtons]
  );

  const handleSwitch = (row: any, value: boolean) => {
    console.log(row, value);
  };

  const handleToDataset = (row: any) => {
    router.push(`/knowledge/dataset?id=${row.id}`);
  };

  return (
    <>
      {/* 头部工具栏 - 一行布局 */}
      <div className="h-[48px] w-full flex items-center justify-between">
        <div className="flex items-center">
          <Button
            icon={<PlusOutlined />}
            color="default"
            variant="solid"
            type="primary"
            onClick={() => knowledgeModalRef.current?.open()}
          >
            创建库
          </Button>
          <div className="flex-1 max-w-[400px] mx-8">
            <div className="flex items-center bg-[#FFFFFF] h-[37px] w-full px-3 border border-[#0000000F] rounded-[8px]">
              <img
                src="/assets/search/search.svg"
                alt="search"
                className="w-4 h-4"
              />
              <input
                className="flex-1 min-w-0 h-full px-2 bg-transparent font-normal outline-none text-sm text-[#000000B8] placeholder:text-[#00000066]"
                type="text"
                placeholder="搜索"
                onChange={e => handleInputChange(e)}
              />
            </div>
          </div>
        </div>

        {/* 标签筛选 */}
        <div className="flex items-center gap-3">
          <Select
            className="w-[200px]"
            placeholder="标签"
            mode="multiple"
            allowClear={true}
            value={tags}
            onChange={value => setTags(value)}
          >
            {kbTags?.map((tag, tagIndex) => (
              <Option key={tagIndex} value={tag}>
                {tag}
              </Option>
            ))}
          </Select>
        </div>
      </div>
      {/* 左侧：Tab切换 */}
      <div className="flex items-center">
        <button
          className={`px-3 py-[6px] rounded-[8px] text-[13px] font-medium transition-colors cursor-pointer ${
            activeTab === 'personal'
              ? 'bg-[#0000000A] text-[#000000E0]'
              : 'bg-transparent text-[#00000066]'
          }`}
          onClick={() => setActiveTab('personal')}
        >
          个人
        </button>
        <button
          className={`px-3 py-[6px] rounded-[8px] text-[13px] font-medium transition-colors cursor-pointer ${
            activeTab === 'shared'
              ? 'bg-[#0000000A] text-[#000000E0]'
              : 'bg-transparent text-[#00000066]'
          }`}
          onClick={() => setActiveTab('shared')}
        >
          共享
        </button>
      </div>

      <Content
        className="!flex-1 !basis-0 overflow-auto w-[calc(100%+18px)] p-[9px] ml-[-9px]"
        id="scrollableDiv"
      >
        <Spin
          wrapperClassName="block h-full relative [&_.ant-spin-container]:h-full"
          spinning={loading}
        >
          <InfiniteScroll
            dataLength={nextList?.length ?? 0}
            next={fetchNextPage}
            hasMore={hasNextPage}
            loader={
              <Skeleton
                avatar={{ shape: 'circle' }}
                paragraph={{
                  rows: 2,
                }}
              />
            }
            endMessage={!!total && <Divider>没有更多数据了 🤐</Divider>}
            scrollableTarget="scrollableDiv"
            scrollThreshold="200px"
            style={{
              overflow: 'unset',
            }}
          >
            <div className="grid grid-cols-4 gap-4">
              {nextList?.length > 0 ? (
                nextList.map((item, index) => {
                  return (
                    <KnowledgeCard
                      key={`${item?.name}-${index}`}
                      tag={
                        <>
                          {item.permission === '0' ? (
                            <Tag className="rounded-[4px]" color="warning">
                              公开
                            </Tag>
                          ) : (
                            <Tag className="rounded-[4px]" color="processing">
                              部分成员可访问
                            </Tag>
                          )}
                          {(item?.tags as string[]).map((tag, tagIndex) => (
                            <Tag
                              key={tagIndex}
                              className="rounded-[4px] bg-white text-[#5c5c5c] border-[#ebebeb]"
                            >
                              {tag}
                            </Tag>
                          ))}
                        </>
                      }
                      doc_num={item.doc_num}
                      footerTag={
                        <div
                          className={`size-[8px] rounded-[3px] border ${
                            item.status === '1'
                              ? 'border-[#309b4c] bg-[#34a853]'
                              : 'border-[#d54135] bg-[#e74639]'
                          }`}
                        ></div>
                      }
                      actionButtons={getActionButtons(item)}
                      description={item.description}
                      title={item.name}
                      createTime={formatDate(
                        item.update_time,
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                      handleSwitch={value => handleSwitch(item, value)}
                      handleAction={type => handleAction(item, type)}
                      onClick={() => handleToDataset(item)}
                      status={item.status}
                    />
                  );
                })
              ) : (
                <EmptyData />
              )}
            </div>
          </InfiniteScroll>
        </Spin>
      </Content>
      <KnowledgeModal ref={knowledgeModalRef} />
      <DeleteKnowledgeModal ref={deleteKnowledgeModalRef} />
    </>
  );
}
