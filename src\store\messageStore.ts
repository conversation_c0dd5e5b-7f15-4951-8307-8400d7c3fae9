'use client';

import { ChatMessage } from '@/lib/chat-engine/types';
import { webSocketManager } from '@/lib/message/WebSocketManager';
import { conversationApi } from '@/services/apiManager';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// 消息状态接口
interface MessageState {
  // 数据
  messages: ChatMessage[];
  currentConversationId: string | null;

  // 基础状态
  loading: boolean;
  error: string | null;

  // 流式消息状态
  isStreaming: boolean;
  streamingMessageId: string | null;

  // 工具调用状态
  // activeToolCalls: Map<string, ToolCallExecution>;

  // 连接和运行状态
  connected: boolean;
  isRunning: boolean;
}

// 消息操作接口
interface MessageActions {
  // 加载消息
  loadMessages: (conversationId: string) => Promise<void>;

  // 消息管理
  addMessage: (message: ChatMessage) => void;
  updateStreamingMessage: (messageId: string, delta: string) => void;
  finishStreamingMessage: (messageId: string) => void;
  sendMessage: (agentId: string, content: string) => Promise<void>;

  // 状态管理
  clearMessages: () => void;
  clearError: () => void;
  setError: (error: string | null) => void;

  // 流式状态
  setStreamingState: (isStreaming: boolean, messageId?: string) => void;

  // 工具调用状态
  setToolCallStart: (toolCallId: string, toolName: string) => void;
  setToolCallArgs: (toolCallId: string, args: string) => void;
  setToolCallEnd: (toolCallId: string) => void;
  setToolCallResult: (toolCallId: string) => void;

  // 连接和运行状态
  setConnectionState: (connected: boolean) => void;
  setRunningState: (isRunning: boolean) => void;

  // WebSocket 管理
  initializeWebSocket: (token?: string) => void;
  connectToConversation: (
    conversationId: string,
    token?: string
  ) => Promise<void>;
  disconnectWebSocket: () => Promise<void>;
}

// 简单的消息缓存
const messagesCache = new Map<
  string,
  { messages: ChatMessage[]; timestamp: number }
>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟过期

// WebSocket 全局状态管理 - 确保只初始化一次
let webSocketInitialized = false;

// 创建精简的消息store
export const useMessageStore = create<MessageState & MessageActions>()(
  immer((set, get) => ({
    // === 初始状态 ===
    messages: [],
    currentConversationId: null,
    loading: false,
    error: null,

    // 流式消息状态
    isStreaming: false,
    streamingMessageId: null,

    // 工具调用状态
    activeToolCalls: new Map(),

    // 连接和运行状态
    connected: false,
    isRunning: false,

    // === 消息加载 ===
    loadMessages: async (conversationId: string) => {
      const state = get();

      // 如果是同一个会话且已经有消息，跳过重复加载
      if (
        state.currentConversationId === conversationId &&
        state.messages.length > 0
      ) {
        console.log('📂 [MessageStore] 跳过重复加载:', { conversationId });
        return;
      }

      // 检查缓存
      const cached = messagesCache.get(conversationId);
      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        console.log('📂 [MessageStore] 使用缓存消息:', { conversationId });
        set(state => {
          state.messages = cached.messages;
          state.currentConversationId = conversationId;
          state.error = null;
        });
        return;
      }

      console.log('📂 [MessageStore] 开始加载消息:', { conversationId });

      set(state => {
        state.loading = true;
        state.error = null;
        state.currentConversationId = conversationId;
      });

      try {
        const response =
          await conversationApi.getConversationMessages(conversationId);

        if (!response || !Array.isArray(response)) {
          console.warn('⚠️ [MessageStore] API返回数据格式异常:', response);
          set(state => {
            state.messages = [];
            state.loading = false;
          });
          return;
        }

        // 转换消息格式以兼容ChatMessage接口
        const chatMessages: ChatMessage[] = response.map((msg: any) => ({
          id: msg.id || `msg_${Date.now()}_${Math.random()}`,
          conversationId: conversationId,
          role: msg.role === 'user' ? 'user' : 'assistant',
          content: msg.text || '',
          timestamp: msg.timestamp
            ? new Date(msg.timestamp).getTime()
            : Date.now(),
          status: 'sent',
          // 保留原始数据以备使用
          originalData: msg,
        }));

        // 缓存消息
        messagesCache.set(conversationId, {
          messages: chatMessages,
          timestamp: Date.now(),
        });

        set(state => {
          state.messages = chatMessages;
          state.loading = false;
          state.error = null;
        });

        console.log('✅ [MessageStore] 消息加载完成:', {
          conversationId,
          messageCount: chatMessages.length,
        });
      } catch (error: any) {
        console.error('❌ [MessageStore] 加载消息失败:', error);
        set(state => {
          state.error = error.message || '加载消息失败';
          state.loading = false;
          state.messages = [];
        });
      }
    },

    // === 状态管理 ===
    clearMessages: () => {
      set(state => {
        state.messages = [];
        state.currentConversationId = null;
        state.error = null;

        // 重置流式状态
        state.isStreaming = false;
        state.streamingMessageId = null;

        // 清空工具调用状态
        // state.activeToolCalls.clear();

        // 重置运行状态
        state.isRunning = false;
      });
    },

    clearError: () => {
      set(state => {
        state.error = null;
      });
    },

    setError: (error: string | null) => {
      set(state => {
        state.error = error;
      });
    },

    // === 消息管理 ===
    addMessage: (message: ChatMessage) => {
      set(state => {
        // 检查消息是否已存在
        const existingIndex = state.messages.findIndex(
          m => m.id === message.id
        );
        if (existingIndex >= 0) {
          state.messages[existingIndex] = message;
        } else {
          state.messages.push(message);
        }

        // 更新缓存
        if (state.currentConversationId) {
          messagesCache.set(state.currentConversationId, {
            messages: [...state.messages],
            timestamp: Date.now(),
          });
        }
      });
    },

    updateStreamingMessage: (messageId: string, delta: string) => {
      set(state => {
        const messageIndex = state.messages.findIndex(m => m.id === messageId);
        if (messageIndex >= 0) {
          state.messages[messageIndex].content += delta;
          state.messages[messageIndex].isStreaming = true;
        } else {
          // 创建新的流式消息
          const newMessage: ChatMessage = {
            id: messageId,
            conversationId: state.currentConversationId!,
            role: 'assistant',
            content: delta,
            timestamp: Date.now(),
            isStreaming: true,
            status: 'pending',
          };
          state.messages.push(newMessage);
        }
      });
    },

    finishStreamingMessage: (messageId: string) => {
      set(state => {
        const messageIndex = state.messages.findIndex(m => m.id === messageId);
        if (messageIndex >= 0) {
          state.messages[messageIndex].isStreaming = false;
          state.messages[messageIndex].status = 'delivered';
        }

        // 更新缓存
        if (state.currentConversationId) {
          messagesCache.set(state.currentConversationId, {
            messages: [...state.messages],
            timestamp: Date.now(),
          });
        }
      });
    },

    sendMessage: async (agentId: string, content: string) => {
      const state = get();
      if (!state.currentConversationId) {
        throw new Error('缺少会话ID');
      }

      // 添加用户消息
      const userMessage: ChatMessage = {
        id: `user_${Date.now()}`,
        conversationId: state.currentConversationId,
        role: 'user',
        content,
        timestamp: Date.now(),
        status: 'sending',
      };

      get().addMessage(userMessage);

      try {
        const response = await conversationApi.sendMessage({
          agentId,
          conversationId: state.currentConversationId,
          text: content,
        });
        console.log(
          '[MessageStore] 发送消息 (通过HTTP API):',
          { agentId, content },
          response
        );

        // 更新消息状态为已发送
        // set(state => {
        //   const messageIndex = state.messages.findIndex(m => m.id === userMessage.id);
        //   if (messageIndex >= 0 && response && response.message_id) {
        //     // 用接口返回的消息对象替换原有自定义id的消息
        //     state.messages[messageIndex] = {
        //       id: response.message_id,
        //       conversationId: response.conversation_id,
        //       role: response.role,
        //       content: response.text,
        //       timestamp: new Date(response.created_at).getTime(),
        //       status: 'sent',
        //       isStreaming: response.isStreaming || false,
        //     };
        //   } else if (messageIndex >= 0) {
        //     // 如果没有返回消息对象，仍然标记为已发送
        //     state.messages[messageIndex].status = 'sent';
        //   }
        // });
      } catch (error: any) {
        // 更新消息状态为失败
        set(state => {
          const messageIndex = state.messages.findIndex(
            m => m.id === userMessage.id
          );
          if (messageIndex >= 0) {
            state.messages[messageIndex].status = 'failed';
          }
        });
        throw error;
      }
    },

    // === 流式状态 ===
    setStreamingState: (isStreaming: boolean, messageId?: string) => {
      set(state => {
        state.isStreaming = isStreaming;
        state.streamingMessageId = messageId || null;
      });
    },

    // === 工具调用状态 ===
    setToolCallStart: (toolCallId: string, toolName: string) => {
      // set(state => {
      //   state.activeToolCalls.set(toolCallId, {
      //     id: toolCallId,
      //     name: toolName,
      //     args: '',
      //     status: 'receiving_args',
      //     startTime: Date.now(),
      //   });
      // });
    },

    setToolCallArgs: (toolCallId: string, args: string) => {
      // set(state => {
      //   const toolCall = state.activeToolCalls.get(toolCallId);
      //   if (toolCall) {
      //     toolCall.args = args;
      //   }
      // });
    },

    setToolCallEnd: (toolCallId: string) => {
      // set(state => {
      //   const toolCall = state.activeToolCalls.get(toolCallId);
      //   if (toolCall) {
      //     toolCall.status = 'executing';
      //     try {
      //       toolCall.parsedArgs = JSON.parse(toolCall.args);
      //     } catch (e) {
      //       console.warn('[MessageStore] 工具调用参数解析失败:', toolCall.args);
      //     }
      //   }
      // });
    },

    setToolCallResult: (toolCallId: string) => {
      // set(state => {
      //   const toolCall = state.activeToolCalls.get(toolCallId);
      //   if (toolCall) {
      //     toolCall.status = 'completed';
      //     toolCall.endTime = Date.now();
      //     // 可以选择在一段时间后清理已完成的工具调用
      //     setTimeout(() => {
      //       get().activeToolCalls.delete(toolCallId);
      //     }, 5000);
      //   }
      // });
    },

    // === 连接和运行状态 ===
    setConnectionState: (connected: boolean) => {
      set(state => {
        state.connected = connected;
      });
    },

    setRunningState: (isRunning: boolean) => {
      set(state => {
        state.isRunning = isRunning;
      });
    },

    // === WebSocket 管理 ===
    initializeWebSocket: (_token?: string) => {
      if (webSocketInitialized) {
        return;
      }

      console.log('[MessageStore] 初始化 WebSocket 事件处理器');

      // 设置 WebSocket 事件处理器（全局唯一）
      webSocketManager.setEventHandlers({
        onStreamingStart: messageId => {
          console.log('[MessageStore] 流式消息开始:', messageId);
          get().setStreamingState(true, messageId);
        },
        onStreamingContent: (messageId, delta) => {
          const state = get();
          if (state.currentConversationId) {
            get().updateStreamingMessage(messageId, delta);
          }
        },
        onStreamingEnd: messageId => {
          console.log('[MessageStore] 流式消息结束:', messageId);
          const state = get();
          if (state.currentConversationId) {
            get().finishStreamingMessage(messageId);
          }
          get().setStreamingState(false);
        },
        onMessage: message => {
          console.log('[MessageStore] 消息已发送:', message);
          const state = get();
          if (state.currentConversationId) {
            // 替换最后一条用户消息
            // 先查找当前会话的最后一条用户消息，并用新消息替换
            const lastUserMsgIndex = state.messages
              .slice()
              .reverse()
              .findIndex(
                msg =>
                  msg.conversationId === state.currentConversationId &&
                  ['user', 'client'].includes(msg.role)
              );
            if (lastUserMsgIndex !== -1) {
              // 计算正向索引
              const idx = state.messages.length - 1 - lastUserMsgIndex;
              set(s => {
                s.messages[idx] = message;
              });
            } else {
              // 如果没有找到用户消息，则直接追加
              set(s => {
                s.messages.push(message);
              });
            }
          }
        },
        onToolCallStart: (toolCallId, toolName) => {
          console.log('[MessageStore] 工具调用开始:', { toolCallId, toolName });
          get().setToolCallStart(toolCallId, toolName);
        },
        onToolCallArgs: (toolCallId, delta) => {
          console.log('[MessageStore] 工具调用参数:', {
            toolCallId,
            delta: delta.slice(0, 50),
          });
          // 累加参数
          // const state = get();
          // const existing = state.activeToolCalls.get(toolCallId);
          // if (existing) {
          //   get().setToolCallArgs(toolCallId, existing.args + delta);
          // }
        },
        onToolCallEnd: (toolCallId, finalArgs) => {
          console.log('[MessageStore] 工具调用参数完成:', {
            toolCallId,
            finalArgs: finalArgs.slice(0, 100),
          });
          get().setToolCallArgs(toolCallId, finalArgs);
          get().setToolCallEnd(toolCallId);
        },
        onToolCallResult: (toolCallId, result) => {
          console.log('[MessageStore] 工具调用结果:', {
            toolCallId,
            result: result.slice(0, 100),
          });
          get().setToolCallResult(toolCallId);
        },
        onRunStarted: runId => {
          console.log('[MessageStore] AI运行开始:', runId);
          get().setRunningState(true);
        },
        onRunFinished: runId => {
          console.log('[MessageStore] AI运行完成:', runId);
          get().setRunningState(false);
        },
        onError: errorMessage => {
          console.error('[MessageStore] WebSocket error:', errorMessage);
          get().setError(errorMessage);
        },
        onConnectionChange: connected => {
          console.log('[MessageStore] 连接状态变化:', connected);
          get().setConnectionState(connected);
        },
      });

      webSocketInitialized = true;
    },

    connectToConversation: async (conversationId: string, token?: string) => {
      const state = get();

      // 确保 WebSocket 已初始化
      get().initializeWebSocket(token);

      // 更新当前会话ID
      if (state.currentConversationId !== conversationId) {
        set(draft => {
          draft.currentConversationId = conversationId;
        });
      }

      // 连接到会话（WebSocketManager会自动处理会话切换）
      if (token) {
        try {
          await webSocketManager.connect(conversationId, token);
          console.log(
            '[MessageStore] WebSocket 连接/切换成功:',
            conversationId
          );
        } catch (error) {
          console.error('[MessageStore] WebSocket 连接/切换失败:', error);
          get().setError(`连接失败: ${error}`);
        }
      } else {
        console.warn('[MessageStore] 缺少token，无法建立WebSocket连接');
      }
    },

    disconnectWebSocket: async () => {
      try {
        await webSocketManager.disconnect();
        console.log('[MessageStore] WebSocket 已断开');
      } catch (error) {
        console.error('[MessageStore] WebSocket 断开时出错:', error);
      }
    },
  }))
);
