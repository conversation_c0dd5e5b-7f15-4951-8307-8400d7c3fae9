'use client';

import React from 'react';

interface EnvironmentCheckerProps {
  children: React.ReactNode;
}

/**
 * 环境变量检查组件
 * 统一检查必要的环境变量是否存在，如果不存在则显示错误页面
 */
export default function EnvironmentChecker({
  children,
}: EnvironmentCheckerProps) {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;

  // 收集缺失的环境变量
  const missingEnvVars: string[] = [];

  if (!apiUrl) {
    missingEnvVars.push('NEXT_PUBLIC_API_URL');
  }

  // 如果有缺失的环境变量，显示错误页面
  if (missingEnvVars.length > 0) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-bg-base">
        <div className="max-w-md w-full p-8 text-center space-y-6">
          {/* 错误图标 */}
          <svg
            className="w-16 h-16 mx-auto text-error"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>

          {/* 错误标题 */}
          <div>
            <h1 className="text-xl font-bold text-error mb-2">配置错误</h1>
            <p className="text-text-muted text-sm">
              应用启动失败，缺少必要的环境变量配置
            </p>
          </div>

          {/* 缺失的环境变量列表 */}
          <div className="bg-layer-1 border border-border-light rounded-lg p-4 text-left">
            <h3 className="text-sm font-medium text-text-primary mb-3">
              缺失的环境变量:
            </h3>
            <ul className="space-y-2">
              {missingEnvVars.map(envVar => (
                <li key={envVar} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-error rounded-full flex-shrink-0"></div>
                  <code className="text-xs font-mono text-text-secondary bg-layer-2 px-2 py-1 rounded">
                    {envVar}
                  </code>
                </li>
              ))}
            </ul>
          </div>

          {/* 解决方案指导 */}
          <div className="bg-layer-1 border border-border-light rounded-lg p-4 text-left">
            <h3 className="text-sm font-medium text-text-primary mb-3">
              解决方案:
            </h3>
            <ol className="space-y-2 text-xs text-text-secondary">
              <li className="flex space-x-2">
                <span className="text-primary font-medium flex-shrink-0">
                  1.
                </span>
                <span>
                  检查项目根目录的{' '}
                  <code className="bg-layer-2 px-1 rounded">.env.local</code>{' '}
                  文件
                </span>
              </li>
              <li className="flex space-x-2">
                <span className="text-primary font-medium flex-shrink-0">
                  2.
                </span>
                <span>确保包含所有必要的环境变量</span>
              </li>
              <li className="flex space-x-2">
                <span className="text-primary font-medium flex-shrink-0">
                  3.
                </span>
                <span>重启开发服务器使配置生效</span>
              </li>
            </ol>
          </div>

          {/* 示例配置 */}
          <div className="bg-layer-1 border border-border-light rounded-lg p-4 text-left">
            <h3 className="text-sm font-medium text-text-primary mb-3">
              配置示例:
            </h3>
            <pre className="text-xs font-mono text-text-secondary bg-layer-2 p-3 rounded overflow-x-auto">
              {`# .env.local
  NEXT_PUBLIC_API_URL=https://your-api-url.com
  NEXT_PUBLIC_ZIKO_API_URL=https://your-ziko-api-url.com`}
            </pre>
          </div>

          {/* 重试按钮 */}
          <button
            onClick={() => window.location.reload()}
            className="w-full px-4 py-2 bg-primary text-bg-base rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
          >
            重新检查配置
          </button>

          {/* 帮助链接 */}
          <div className="text-xs text-text-muted">
            <p>需要帮助？请查看项目文档或联系开发团队</p>
          </div>
        </div>
      </div>
    );
  }

  // 如果所有环境变量都存在，渲染子组件
  return <>{children}</>;
}
