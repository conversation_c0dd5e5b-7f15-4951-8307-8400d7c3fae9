'use client';
import { DocumentParserType } from '@/constants/knowledge';
import { useFetchNextDocumentList } from '@/hooks/document-hooks';
import { useSetSelectedRecord } from '@/hooks/logic-hooks';
import type { IDocumentInfo } from '@/interfaces/document';
import type { IChangeParserConfigRequestBody } from '@/interfaces/request/document';
import { getExtension } from '@/utils/document-util';
import { useRouter } from 'next/navigation';
import ChunkMethodModal from '../_components/chunk-method-modal';
import DocumentList from '../_components/document-list';
import FilePicker from '../_components/file-picker';
import CreateFileModal from './create-file-modal';
import DocumentToolbar from './document-toolbar';
import {
  useChangeDocumentParser,
  useCreateEmptyDocument,
  useGetRowSelection,
  useHandleAddFileToKnowledge,
  useHandleRunDocumentByIds,
  useNavigateToOtherPage,
  useRenameDocument,
  useShowMetaModal,
} from './hooks';
import RenameModal from './rename-modal';

const KnowledgeFile = () => {
  const router = useRouter();
  const { rowSelection, showSelection, setShowSelection } =
    useGetRowSelection();
  const { searchString, documents, pagination, handleInputChange } =
    useFetchNextDocumentList();

  const {
    createLoading,
    onCreateOk,
    createVisible,
    hideCreateModal,
    showCreateModal,
  } = useCreateEmptyDocument();

  const { toChunk } = useNavigateToOtherPage();

  const {
    addFileToKnowledgeVisible,
    hideAddFileToKnowledgeModal,
    showAddFileToKnowledgeModal,
    onAddFileToKnowledgeOk,
  } = useHandleAddFileToKnowledge();

  const { currentRecord, setRecord } = useSetSelectedRecord<IDocumentInfo>();

  const {
    renameLoading,
    onRenameOk,
    renameVisible,
    hideRenameModal,
    showRenameModal,
  } = useRenameDocument(currentRecord.id);

  const {
    changeParserLoading,
    onChangeParserOk,
    changeParserVisible,
    hideChangeParserModal,
    showChangeParserModal,
  } = useChangeDocumentParser(currentRecord.id);

  const { handleRunDocumentByIds, loading: runLoading } =
    useHandleRunDocumentByIds(currentRecord.id);

  /**
   * 处理切片方法弹窗确认：保存配置后直接执行解析
   * @param parserId 解析器ID
   * @param parserConfig 解析器配置
   */
  const handleChunkMethodModalOk = async (
    parserId: DocumentParserType,
    parserConfig: IChangeParserConfigRequestBody,
    embed: boolean
  ) => {
    // 先保存配置
    // await onChangeParserOk(parserId, parserConfig);
    // 配置保存成功后执行解析
    await handleRunDocumentByIds(
      currentRecord.id,
      false,
      parserConfig,
      embed,
      false
    );
    hideChangeParserModal();
  };

  const {
    showSetMetaModal,
    hideSetMetaModal,
    setMetaVisible,
    setMetaLoading,
    onSetMetaModalOk,
  } = useShowMetaModal(currentRecord.id);

  return (
    <>
      <DocumentToolbar
        selectedRowKeys={rowSelection.selectedRowKeys as string[]}
        showCreateModal={showCreateModal}
        // showDocumentUploadModal={showDocumentUploadModal}
        showDocumentUploadModal={showAddFileToKnowledgeModal}
        searchString={searchString}
        handleInputChange={handleInputChange}
        documents={documents}
        total={pagination.total}
        setShowSelection={setShowSelection}
      ></DocumentToolbar>

      {/* 文档列表 */}
      <DocumentList
        documents={documents}
        rowSelection={rowSelection}
        showSelection={showSelection}
        pagination={pagination}
        onDocumentClick={toChunk}
        setCurrentRecord={setRecord}
        showRenameModal={showRenameModal}
        showChangeParserModal={showChangeParserModal}
        showSetMetaModal={showSetMetaModal}
      />
      <CreateFileModal
        visible={createVisible}
        hideModal={hideCreateModal}
        loading={createLoading}
        onOk={onCreateOk}
      />

      <FilePicker
        multiple={true}
        title="选择文件"
        visible={addFileToKnowledgeVisible}
        onCancel={hideAddFileToKnowledgeModal}
        onConfirm={onAddFileToKnowledgeOk}
      />

      <RenameModal
        visible={renameVisible}
        onOk={onRenameOk}
        loading={renameLoading}
        hideModal={hideRenameModal}
        initialName={currentRecord.name}
      ></RenameModal>

      <ChunkMethodModal
        documentId={currentRecord.id}
        parserId={currentRecord.parser_id as DocumentParserType}
        parserConfig={currentRecord.parser_config}
        documentExtension={getExtension(currentRecord.name)}
        onOk={handleChunkMethodModalOk}
        visible={changeParserVisible}
        hideModal={hideChangeParserModal}
        loading={changeParserLoading || runLoading}
      />
    </>
  );
};

export default KnowledgeFile;
